@props([
    'parent' => 'data-table', // Inherit from data-table template
    'include_column_manager' => false,
    'oob-swap' => false,
    'auto_update' => false
])

@php
    // Ensure columns is always an array to prevent count() errors
    $columns = $columns ?? [];
    $column_preferences = $column_preferences ?? [];
    print_rr($auto_update, 'auto_update');
@endphp

@if (empty($columns))
    <!-- No columns configured fallback -->
    <x-alert-box
        type="warning"
        title="No Columns Configured"
        message="This data table has no columns configured. Please configure columns to display data."
        layout="centered"
        size="large"
        class="data_table" />
@else

@if($auto_update)
<div id="auto_update_container" hx-swap-oob="innerHTML">
        <input type="hidden" class="data_table_cfg" name="callback" value="{{ $callback }}">
    <input type="hidden" class="data_table_cfg" name="table_name" value="{{ $table_name }}">
    <input type="hidden" class="data_table_cfg" name="data_source_id" value="{{ $data_source_id }}">
           <x-forms-input
                name='last_update'
                id="data_table_auto_update"
                :value='date("Y-m-d H:i:s")'
                hx-post='{{ APP_ROOT }}/api/data_table/refresh_table'
                hx-swap='outerHTML'
                hx-target=".data_table"
                hx-include=".data_table_cfg"
                hx-trigger='every 10s'
           />
</div>
    @endif
    <table class="min-w-full border-collapse search_target data_table {{ $class }}">
        <thead>
            <tr>
                @foreach($columns as $col)
                    @php
                        $column_info = data_table_storage::process_column_for_display($col, $column_preferences, $loop->index);
                    @endphp
                    @if(!$column_info['is_hidden'])
                        <th scope="col"
                            class="{{ $loop->first ? 'relative sticky top-0 border-b border-gray-300 bg-gray-200 py-1.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8' : 'relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell' }}"
                            style="isolation: isolate;"
                            data-column-field="{{ $column_info['col_field'] }}">
                            <x-data-table-filter
                                    :label="$column_info['col_label']"
                                    :col="$col"
                                    :sort_col="$sort_column"
                                    :sort_dir="$sort_direction"
                                    :callback="$callback"
                                    :id_count="$id_count"
                            ></x-data-table-filter>
                        </th>
                    @endif
                @endforeach
            </tr>
        </thead>
        <tbody class="bg-white data_table_body">
            <x-data-table-rows
                    :items="$items"
                    :columns="$columns"
                    :column_preferences="$column_preferences"
                    :rows="$rows"
                    :items_per_page="$items_per_page"
            />
        </tbody>
        <tfoot>
            <tr>
                <td colspan="{{ count($columns) > 0 ? count($columns) : 1 }}">
                    <x-pagination-strip
                            :item_count="$total_count"
                            :items_per_page="$items_per_page"
                            :current_page_num="$current_page_num"
                            :first_item="($current_page_num - 1) * $items_per_page"
                    ></x-pagination-strip>
                </td>
            </tr>
        </tfoot>
    </table>
    @if($include_column_manager) {

        <x-data-table-column-manager-panel
                :columns="$columns"
                :table_name="$table_name"
                :callback="$callback"
                :column_preferences="$column_preferences"
                :db_table="$db_table"
                :available_fields="$available_fields"
                :current_data_source_type="$current_data_source_type"
                :current_data_source_id="$current_data_source_id"
                oob-swap="true"
        />
    @endif
@endif
