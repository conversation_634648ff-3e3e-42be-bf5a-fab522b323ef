@props([
    'title' => 'data-table',
    'description' => 'table module',
    'items' => [], // data array of items
    'id_count' => edge::id_count(),
    'columns' => [], // An array of column definitions: ['label' => 'Name', 'fields' => ['name'], 'filter' => false]
    'available_fields' => [], // Array of available fields for column manager
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],
    'items_per_page' => 30, //max items to display before pagination
    'current_page_num' => 1,
    'class' => '', //extra classes
    'sort_column' => '',
    'sort_direction' => '',
    'auto_update' => false,
    'callback' => null,
    'table_name' => str_replace('/','_',strtolower(APP_PATH . '_' . CURRENT_PAGE)),
    'db_table' => '',
    'show_column_manager' => true,
    'column_preferences' => [],
    'criteria' => [],
    'config' => null,
    'data_source_type' => 'hardcoded',
    'data_source_id' =>  null,
    'total_count' => 0
])

@init
        tcs_log("Data table template " . pathinfo(__FILE__, PATHINFO_FILENAME) . ": table=$table_name, type=$data_source_type, id=$data_source_id", 'data_table_saga');
        print_rr([  'items' => $items,
            'columns' => $columns,
            'available_fields' => $available_fields,
            'table_name' => $table_name,
            'criteria' => $criteria,
            'data_source_id' => $data_source_id,
            'data_source_type' => $data_source_type,
            'callback' => $callback,
            'column_preferences' => $column_preferences,
            'config' => $config
        ], 'tcs_master01');
    // Master data table initialization - runs once in hierarchy
        $table_data = data_table_storage::prepare_template_data([
            'items' => $items,
            'columns' => $columns,
            'available_fields' => $available_fields,
            'table_name' => $table_name,
            'criteria' => $criteria,
            'data_source_id' => $data_source_id,
            'data_source_type' => $data_source_type,
            'callback' => $callback,
            'column_preferences' => $column_preferences,
            'config' => $config
        ]);
        print_rr($table_data,  'tcs_master02');
        extract($table_data);

        // Mark that master initialization has been completed
        $edge_manifest['_inheritance_execution_state']['master_data_prepared'] = true;
        tcs_log("Data table master initialization completed for: $table_name", 'data_table_inheritance');


@endinit

{{-- @print_rr([
'items' => count($items) > 5 ? array_slice($items, random_int(0, count($items) - 5), 5) : $items,
'count' => count($items),
'columns' => $columns,
'column_count' => count($columns),
'available_fields' => $available_fields
],null,'tcs_items') --}}






<input type="hidden" class="data_table_filter" name="callback" value="{{ $callback }}">
<input type="hidden" class="data_table_filter" name="table_name" value="{{ $table_name }}">
<input type="hidden" class="data_table_filter" name="data_source_id" value="{{ $data_source_id }}">
<div class="relative">
    @if($show_column_manager)
        <div class="absolute top-0 right-0 z-10 p-2">
            <x-data-table-column-manager
                    :columns="$columns"
                    :table_name="$table_name"
                    :callback="$callback"
                    :column_preferences="$column_preferences"
                    :db_table="$db_table"
                    :available_fields="$available_fields"
                    :current_data_source_type="$data_source_type"
                    :current_data_source_id="$data_source_id"
            ></x-data-table-column-manager>
        </div>
    @endif
</div>
@if (empty($columns) && empty($items))
    <!-- No columns and no items configured fallback -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center data_table" >
        <div class="flex items-center justify-center mb-4">
            <svg class="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-medium text-red-800 mb-2">Data Table Not Configured</h3>
        <p class="text-red-700 mb-4">
            This data table has no columns or data configured. Please set up both columns and a data source to display information.
        </p>
        @if($show_column_manager)
            <p class="text-sm text-red-600 mb-2">
                Use the column manager (⚙️) in the top-right corner to configure columns and data sources for this table.
            </p>
        @endif
        <div class="text-xs text-red-500 bg-red-100 rounded p-2 mt-4">
            <strong>Table:</strong> {{ $table_name }}<br>
            <strong>Data Source Type:</strong> {{ $data_source_type ?? 'not set' }}<br>
            <strong>Data Source ID:</strong> {{ $data_source_id ?? 'not set' }}
        </div>
    </div>
@else
    <x-data-table-structure
            :items="$items"
            :columns="$columns"
            :column_preferences="$column_preferences"
            :sort_column="$sort_column"
            :sort_direction="$sort_direction"
            :callback="$callback"
            :id_count="$id_count"
            :class="$class"
            :items_per_page="$items_per_page"
            :current_page_num="$current_page_num"
            :total_count="isset($total_count) ? $total_count : count($items)"
    ></x-data-table-structure>
@endif
