
<div class="p-5">
  <!-- wrapper -->
  <!-- Main content -->

  <!-- Admin Dashboard Section -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">System Administration</h1>
    <p class="text-sm text-gray-600 mb-6">System administration, configuration, and database tools</p>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <!-- Subscription Matching Rules -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Subscription Matching</dt>
                <dd class="text-lg font-medium text-gray-900">Rules Manager</dd>
              </dl>
            </div>
          </div>
          <div class="mt-5">
            <div class="rounded-md shadow">
              <a href="{{ APP_ROOT }}/admin/subscription_matching_rules"
                 class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                Configure Rules
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Hash Fix Tool -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 12 2 2 4-4" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Database Tools</dt>
                <dd class="text-lg font-medium text-gray-900">Hash Fix Tool</dd>
              </dl>
            </div>
          </div>
          <div class="mt-5">
            <div class="rounded-md shadow">
              <a href="{{ APP_ROOT }}/system/fix_autodesk_hashes"
                 class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                Fix Autodesk Hashes
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Edge Template Tester -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Development Tools</dt>
                <dd class="text-lg font-medium text-gray-900">Template Tester</dd>
              </dl>
            </div>
          </div>
          <div class="mt-5">
            <div class="rounded-md shadow">
              <a href="{{ APP_ROOT }}/system/edge_template_tester"
                 class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                Test Templates
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Development Testing Suite -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Development Tools</dt>
                <dd class="text-lg font-medium text-gray-900">Testing Suite</dd>
              </dl>
            </div>
          </div>
          <div class="mt-5">
            <div class="rounded-md shadow">
              <a href="{{ APP_ROOT }}/system/testing_suite"
                 class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                Run Tests
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- System Analytics -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">System</dt>
                <dd class="text-lg font-medium text-gray-900">Analytics</dd>
              </dl>
            </div>
          </div>
          <div class="mt-5">
            <div class="rounded-md shadow">
              <a href="#"
                 class="flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                View Reports
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- System Tools Section -->
  <div class="mb-8">
    <h2 class="text-2xl font-bold text-gray-900 mb-4">API Tools</h2>
    <div class="bg-white shadow rounded-lg p-6">
      <!-- /.card-header -->
      <div id="card-body">
    <x-forms-button
      id="Get_Products"
      label="Get Products"
      hx-post="{{ APP_ROOT }}/api/system/update_products"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />

    <x-forms-button
      id="Get_prices"
      label="Update Prices"
      hx-post="{{ APP_ROOT }}/api/system/update_prices"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />

    <x-forms-button
      label="Get Subscriptions for api"
      hx-post="{{ APP_ROOT }}/api/system/subscriptions_get_from_api"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />
    <x-forms-button
      label="Get Quote JSON from api"
      hx-post="{{ APP_ROOT }}/api/system/quotes_get_from_api"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />

    <x-forms-button
      label="Get Promos"
      hx-target="#output_card_body"
      hx-swap="innerHtml"
      hx-post="{{ APP_ROOT }}/api/system/autodesk/get_promos_from_api"
      :hx-vals='{{ json_encode(["action" => "autodesk_get_promos_csv"]) }}'
    />

    <x-forms-form
      id="autodesk_search_customers_form"
      class="form-control"
      hx-post="{{ APP_ROOT }}/api/system/api_h.php"
      hx-swap="innerHtml"
      hx-target="#output_card_body"
      hx-vals='{{ json_encode(["action" => "autodesk_search_customers"]) }}'
    >

    <h3>Search Customers</h3>
    <x-forms-input
      type="text"
      name="name"
      id="name_input"
      placeholder="name"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="countryCode"
      id="countryCode_input"
      placeholder="countryCode"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="contactEmail"
      id="email_input"
      placeholder="Email"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="subscriptionId"
      id="subscriptionId_input"
      placeholder="subscriptionId"
      class="form-control"
      required
    />

    <x-forms-input
      type="text"
      name="endpoint"
      id="endpoint_input"
      placeholder="endpoint"
      class="form-control"
      required
    />
    <x-forms-button
      label="Submit"
      class="btn btn-primary"
    />
    </x-forms-form><br><br>


    <h3>Get opportunity</h3>

    <x-forms-form
      id="autodesk_get_opportunity_form"
      hx-post="{{ APP_ROOT }}/api_h.php"
      hx-swap="innerHtml"
      hx-target="#output_card_body"
      hx-vals='{{ json_encode(["action" => "autodesk_get_opportunity"]) }}'
    >
    <x-forms-input
      type="text"
      name="endCustomerCsn"
      id="endCustomerCsn_input"
      placeholder="endCustomerCsn"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="opportunityNumber"
      id="opportunityNumber_input"
      placeholder="opportunityNumber"
      class="form-control"
    />

    <x-forms-button
      label="Submit"
      class="btn btn-primary"
    />

    </x-forms-form><br><br>
    <x-forms-form
      id="autodesk_raw_api_call_form"
      hx-post="{{ APP_ROOT }}/api/system/autodesk_send_raw_api_call"
      hx-swap="innerHtml"
      hx-target="#output_card_card_body"
      hx-vals='{{ json_encode(["action" => "autodesk_raw_api_call"]) }}'
    >
    <x-forms-input
      type="text"
      name="requestType"
      id="requestType_input"
      placeholder="requestType (POST, GET)"
      class="form-control"
      required
    />
    <x-forms-input
      type="text"
      name="endpoint"
      id="endpoint_input"
      placeholder="endpoint"
      class="form-control"
      required
    />
    <x-forms-input
      type="text"
      name="downloadFilepath"
      id="downloadFilepath_input"
      placeholder="downloadFilepath"
      class="form-control"
      required
    />

    <x-forms-textarea
      name="params"
      id="params_textarea"
      placeholder="params"
      label="params (php assoc array)"
      class="form-control"
    />

    <x-forms-textarea
      name="json"
      id="json"
      placeholder="json here"
      label="input (json)"
      class="form-control"
    />

    <button type="submit">Send Raw</button>
    </x-forms-form><br><br>


      <x-layout-card
        id="output_card"
        label="Output"
      >
        <div id="output_card_card_body"></div>
      </x-layout-card>
    </div>
  </div>
</div>
