@props([
    'test_results' => [],
    'available_tests' => [],
    'test_categories' => []
])

<div class="p-6">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Development Testing Suite</h1>
        <p class="text-sm text-gray-600 mb-6">Run tests with proper system initialization, database access, and CSS loading</p>
    </div>

    <!-- Test Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Quick Tests -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Tests</h3>
            <div class="space-y-3">
                <button
                    class="w-full text-left px-4 py-2 bg-green-50 hover:bg-green-100 rounded-md border border-green-200 text-green-800"
                    hx-post="{{ APP_ROOT }}/api/system/testing_suite/database_connection"
                    hx-target="#test-results"
                    hx-swap="innerHTML">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                        </svg>
                        Database Connection
                    </div>
                </button>

                <button
                    class="w-full text-left px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-md border border-blue-200 text-blue-800"
                    hx-post="{{ APP_ROOT }}/api/system/testing_suite/edge_templates"
                    hx-target="#test-results"
                    hx-swap="innerHTML">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        Edge Templates
                    </div>
                </button>

                <button
                    class="w-full text-left px-4 py-2 bg-purple-50 hover:bg-purple-100 rounded-md border border-purple-200 text-purple-800"
                    hx-post="{{ APP_ROOT }}/api/system/testing_suite/system_classes"
                    hx-target="#test-results"
                    hx-swap="innerHTML">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        System Classes
                    </div>
                </button>
            </div>
        </div>

        <!-- Custom Tests -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Custom Tests</h3>
            <form hx-post="{{ APP_ROOT }}/api/system/testing_suite/custom_test" hx-target="#test-results" hx-swap="innerHTML">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Name</label>
                    <input type="text" name="test_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="my_test">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Code</label>
                    <textarea name="test_code" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm" placeholder="// Your PHP test code here
$result = 'Hello World';
return $result;"></textarea>
                </div>
                <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                    Run Custom Test
                </button>
            </form>
        </div>

        <!-- Test Files -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Test Files</h3>
            <div class="space-y-4">
                <button
                    class="w-full text-left px-4 py-3 bg-yellow-50 hover:bg-yellow-100 rounded-md border border-yellow-200 text-yellow-800 transition-colors"
                    hx-get="{{ APP_ROOT }}/api/system/testing_suite/list_test_files"
                    hx-target="#test-files-list"
                    hx-swap="innerHTML">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0"></path>
                            </svg>
                            <span class="font-medium">Browse Test Files</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    <div class="text-xs text-yellow-600 mt-1">Click to view organized test files by category</div>
                </button>

                <div id="test-files-list" class="bg-gray-50 rounded-md p-3 min-h-[100px] border-2 border-dashed border-gray-200">
                    <div class="text-center text-gray-500 text-sm py-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0"></path>
                        </svg>
                        Click "Browse Test Files" to see organized test files
                    </div>
                </div>

                <form hx-post="{{ APP_ROOT }}/api/system/testing_suite/test_file" hx-target="#test-results" hx-swap="innerHTML" class="border-t pt-4">
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Test File Path</label>
                        <input type="text" name="test_file" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" placeholder="system/tests/database/test_database_methods.php">
                        <div class="text-xs text-gray-500 mt-1">Enter the full path or click a file above to auto-populate</div>
                    </div>
                    <button type="submit" class="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 text-sm font-medium transition-colors">
                        Run Test File
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Test Directory Overview -->
    <div class="mb-8">
        <div id="test-overview"
             hx-get="{{ APP_ROOT }}/api/system/testing_suite/test_directory_overview"
             hx-trigger="load"
             hx-swap="innerHTML">
            <div class="bg-gray-100 rounded-lg p-6 text-center">
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-300 rounded w-1/4 mx-auto mb-2"></div>
                    <div class="h-3 bg-gray-300 rounded w-1/2 mx-auto"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Results -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Test Results</h3>
        </div>
        <div id="test-results" class="p-6">
            <div class="text-center text-gray-500 py-8">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <p>Select a test to run and see results here</p>
            </div>
        </div>
    </div>

    <!-- Test History -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Test History</h3>
        </div>
        <div class="p-6">
            <div id="test-history"
                 hx-get="{{ APP_ROOT }}/api/system/testing_suite/get_history"
                 hx-trigger="load"
                 hx-swap="innerHTML">
                <div class="text-center text-gray-500 py-4">Loading test history...</div>
            </div>
        </div>
    </div>
</div>
