@props([
    'test_results' => [],
    'available_tests' => [],
    'test_categories' => []
])

<div class="p-6">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Development Testing Suite</h1>
        <p class="text-sm text-gray-600 mb-6">Run tests with proper system initialization, database access, and CSS loading</p>
    </div>

    <!-- Test Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Quick Tests -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Tests</h3>
            <div class="space-y-3">
                <button 
                    class="w-full text-left px-4 py-2 bg-green-50 hover:bg-green-100 rounded-md border border-green-200 text-green-800"
                    hx-post="{{ APP_ROOT }}/api/system/testing_suite"
                    hx-vals='{"test_type": "database_connection"}'
                    hx-target="#test-results"
                    hx-swap="innerHTML">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                        </svg>
                        Database Connection
                    </div>
                </button>
                
                <button 
                    class="w-full text-left px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-md border border-blue-200 text-blue-800"
                    hx-post="{{ APP_ROOT }}/api/system/testing_suite"
                    hx-vals='{"test_type": "edge_templates"}'
                    hx-target="#test-results"
                    hx-swap="innerHTML">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        Edge Templates
                    </div>
                </button>
                
                <button 
                    class="w-full text-left px-4 py-2 bg-purple-50 hover:bg-purple-100 rounded-md border border-purple-200 text-purple-800"
                    hx-post="{{ APP_ROOT }}/api/system/testing_suite"
                    hx-vals='{"test_type": "system_classes"}'
                    hx-target="#test-results"
                    hx-swap="innerHTML">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        System Classes
                    </div>
                </button>
            </div>
        </div>

        <!-- Custom Tests -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Custom Tests</h3>
            <form hx-post="{{ APP_ROOT }}/api/system/testing_suite" hx-target="#test-results" hx-swap="innerHTML">
                <input type="hidden" name="test_type" value="custom">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Name</label>
                    <input type="text" name="test_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="my_test">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Code</label>
                    <textarea name="test_code" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm" placeholder="// Your PHP test code here
$result = 'Hello World';
return $result;"></textarea>
                </div>
                <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                    Run Custom Test
                </button>
            </form>
        </div>

        <!-- Test Files -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Test Files</h3>
            <div class="space-y-3">
                <button 
                    class="w-full text-left px-4 py-2 bg-yellow-50 hover:bg-yellow-100 rounded-md border border-yellow-200 text-yellow-800"
                    hx-get="{{ APP_ROOT }}/api/system/testing_suite"
                    hx-vals='{"action": "list_test_files"}'
                    hx-target="#test-files-list"
                    hx-swap="innerHTML">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        List Test Files
                    </div>
                </button>

                <div id="test-files-list" class="mt-4"></div>

                <form hx-post="{{ APP_ROOT }}/api/system/testing_suite" hx-target="#test-results" hx-swap="innerHTML" class="mt-4">
                    <input type="hidden" name="test_type" value="file">
                    <div class="mb-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Test File</label>
                        <input type="text" name="test_file" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" placeholder="tests/my_test.php">
                    </div>
                    <button type="submit" class="w-full bg-orange-600 text-white px-3 py-2 rounded-md hover:bg-orange-700 text-sm">
                        Run Test File
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Test Results -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Test Results</h3>
        </div>
        <div id="test-results" class="p-6">
            <div class="text-center text-gray-500 py-8">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <p>Select a test to run and see results here</p>
            </div>
        </div>
    </div>

    <!-- Test History -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Test History</h3>
        </div>
        <div class="p-6">
            <div id="test-history"
                 hx-get="{{ APP_ROOT }}/api/system/testing_suite"
                 hx-vals='{"action": "get_history"}'
                 hx-trigger="load"
                 hx-swap="innerHTML">
                <div class="text-center text-gray-500 py-4">Loading test history...</div>
            </div>
        </div>
    </div>
</div>
