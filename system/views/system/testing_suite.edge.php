@props([
    'test_results' => [],
    'available_tests' => [],
    'test_categories' => []
])

<div class="p-6">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Development Testing Suite</h1>
        <p class="text-sm text-gray-600 mb-6">Run tests with proper system initialization, database access, and CSS loading</p>
    </div>

    <!-- Main Layout: Test Tree (Left) + Results (Right) -->
    <div class="grid grid-cols-12 gap-6 h-[calc(100vh-200px)]">
        <!-- Test Files Tree - Left Side -->
        <div class="col-span-2 bg-white shadow rounded-lg overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0"></path>
                    </svg>
                    Test Files
                </h3>
            </div>
            <div class="p-4 overflow-y-auto h-full">
                <div id="test-files-tree"
                     hx-get="{{ APP_ROOT }}/api/system/testing_suite/list_test_files"
                     hx-trigger="load"
                     hx-swap="innerHTML">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <span class="ml-2 text-gray-500">Loading test files...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Area - Right Side -->
        <div class="col-span-10 flex flex-col">
            <!-- Test Directory Overview -->
            <div class="mb-4">
                <div id="test-overview"
                     hx-get="{{ APP_ROOT }}/api/system/testing_suite/test_directory_overview"
                     hx-trigger="load"
                     hx-swap="innerHTML">
                    <div class="bg-gray-100 rounded-lg p-4 text-center">
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-300 rounded w-1/4 mx-auto mb-2"></div>
                            <div class="h-3 bg-gray-300 rounded w-1/2 mx-auto"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white shadow rounded-lg flex-1 flex flex-col">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Test Results</h3>
                </div>
                <div id="test-results" class="p-6 flex-1 overflow-y-auto">
                    <div class="text-center text-gray-500 py-12">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <p class="text-lg font-medium mb-2">Ready to Run Tests</p>
                        <p class="text-sm">Click on any test file from the tree on the left to execute it</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
