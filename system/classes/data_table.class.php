<?php
namespace data_table;
use edge\Edge;
use system\database;
use system\data_table_storage;



/**
 * Criteria Schema

function get_criteria_schema(): array {
    return [
        // Main query parameters
        'limit' => 30,                   // int: Number of records to return
        'offset' => 0,                   // int: Starting point for fetching records
        'order_by' => 'field_name',      // string: Column name to sort by
        'order_direction' => 'ASC',      // string: Sort direction (ASC or DESC)

        // Where conditions
        'where' => [
            'table.column_name' => ['=', 'value'],
            'and' => [
                'table.column_name' => ['=', 'value'],// Array: [operator, value]
            ],

            'or' => [
                'table.column_name' => ['=', 'value'],// Array: [operator, value]
            ]
            // Possible operators: =, !=, >, <, >=, <=, LIKE, IN, NOT IN, IS NULL, IS NOT NULL
        ],

        // Search parameters
        'search' => [
            'term' => '',                // string: Search term
            'columns' => [               // array: Columns to search in
                'table1.column1',
                'table2.column2'
            ]
        ],

        // Join conditions
        'joins' => [
            [
                'type' => 'LEFT',        // string: JOIN type (LEFT, RIGHT, INNER)
                'table' => 'table_name', // string: Table to join
                'on' => [                // array: Join conditions
                    'table1.id' => 'table2.foreign_id'
                ]
            ]
        ],

        // Group by
        'group_by' => [                 // array: Columns to group by
            'table.column1',
            'table.column2'
        ],

        // Having conditions (for grouped results)
        'having' => [
            'column' => ['>', 0]        // Array: [operator, value]
        ],

        // Search columns for full-text search
        'search_columns' => [           // array: Columns to include in full-text search
            'table1.column1',
            'table2.column2'
        ],

        // Distinct selection
        'distinct' => true,             // boolean: Whether to select distinct records

        // Custom SQL fragments
        'custom_sql' => '',             // string: Raw SQL to append to query

        // Debug mode
        'debug' => false                // boolean: Whether to return query string instead of executing
    ];
}


$example_criteria = [
    'limit' => 10,
    'order_by' => 'subs_enddatediff',
    'where' => [
        'subs.status' => ['=', 'Active'],
        'subs.endDate' => ['>', date('Y-m-d')]
    ],
    'search_columns' => [
        'subs.subscriptionReferenceNumber',
        'subs.offeringName',
        'endcust.name',
        'endcust.email'
    ],
    'joins' => [
        [
            'type' => 'LEFT',
            'table' => 'endcust',
            'on' => ['subs.customer_id' => 'endcust.id']
        ]
    ]
];

*/
class data_table {
    public static array $table_structure;
    public static array $data;
    public static array $items;
    public static string $db_table;
    public static array $distinct_columns;
    public static array $replacements;
    public static array $criteria;
    public static array $cols_hidden;
    public static bool $just_body;
    public static bool $just_rows;
    public static bool $just_table;
    public static bool $htmx_oob;
    public static string $callback;
    public static string $db_name;
    public static array $available_fields;

    public static function process_data_table(
        array $table_structure,
        array $items,
        string $db_table = '',
        string $callback = '',
        array $replacements = [],
        array $criteria = [],
        array $cols_hidden = [],
        bool $just_body = false,
        bool $just_rows = false,
        bool $just_table = false,
        $htmx_oob = false,
        int $total_count = null,
        string $table_name = '',
        array $available_fields = [],
        bool $include_column_manager = false
    ): string {
        self::$table_structure = $table_structure;
        self::$db_table = $db_table ?? $table_structure['db_table'] ?? '';
        self::$available_fields = $available_fields;
        self::$items = $items;
        self::$callback = ($callback == '') ? debug_backtrace()[0]['function'] : $callback;
        self::$replacements = $replacements;
        self::$criteria = $criteria;
        self::$cols_hidden = $cols_hidden;
        self::$just_body = $just_body;
        self::$just_rows = $just_rows;
        self::$just_table = $just_table;
        self::$htmx_oob = $htmx_oob;


//        foreach ($cols_hidden as $col) {
//            unset($row_content[$col]);
//        }

        if ($htmx_oob) $row = true;

        // build_column_filters

        self::process_column_filters();
        self::process_string_replacements();

        $htmx_oob_out = "";
        if ($htmx_oob) {
            $htmx_oob_out = [
                "hx-swap-oob" => "true",
                "hx-ext" => "class-tools",
                "hx-classes" => "add htmx-settling, remove:htmx-settling:10s"
            ];
        }

        $sort_column = $criteria['order_by']['column'] ?? (self::$table_structure['columns'][0]['fields'][0] ?? '');
        $sort_direction = $criteria['order_by']['direction'] ?? 'ASC';

        // Get or create column configuration from database
        $column_preferences = [];
        $original_columns = self::build_column_filters();
        $original_data = self::$items;

        // Check if column preferences are passed in criteria (for reordering)
        if (isset($criteria['column_preferences'])) {
            $column_preferences = $criteria['column_preferences'];
        }

        if (!empty($table_name) && empty($column_preferences)) {
            $user_id = data_table_storage::get_current_user_id();

            // Check if configuration exists in database
            $stored_config = data_table_storage::get_configuration($table_name, $user_id);

            if ($stored_config) {
                // Use existing configuration from database
                $column_preferences = $stored_config['configuration'];

                // Check if we should use data source instead of hardcoded data
                $data_source_type = $column_preferences['data_source_type'] ?? 'hardcoded';
                if ($data_source_type === 'data_source') {
                    $data_result = data_table_storage::get_table_data($table_name, $original_data, self::$criteria, $user_id);
                    if ($data_result['success'] && $data_result['source'] !== 'hardcoded_fallback') {
                        self::$data = $data_result['data'];

                        // If data source has different columns, update the column structure
                        if (isset($data_result['data_source_id'])) {
                            $data_source_columns = self::get_data_source_columns($data_result['data_source_id']);
                            if (!empty($data_source_columns)) {
                                // Update column preferences structure if needed
                                if (empty($column_preferences['structure']) || count($column_preferences['structure']) !== count($data_source_columns)) {
                                    $column_preferences['structure'] = $data_source_columns;
                                }
                            }
                        }
                    }
                }
            } else {
                // No configuration exists
                if (!empty($original_columns)) {
                    // Create default configuration from provided columns
                    $column_preferences = data_table_storage::initialize_default_configuration(
                        $table_name,
                        $original_columns,
                        $user_id
                    );
                } else {
                    // No columns provided - check if there's existing configuration to preserve data source
                    $existing_config = data_table_storage::get_configuration($table_name, $user_id);
                    $existing_data_source_type = 'hardcoded';
                    $existing_data_source_id = null;

                    if ($existing_config) {
                        $existing_data_source_type = $existing_config['configuration']['data_source_type'] ?? 'hardcoded';
                        $existing_data_source_id = $existing_config['data_source_id'] ?? $existing_config['configuration']['data_source_id'] ?? null;
                    }

                    // Create blank table configuration preserving data source settings
                    $column_preferences = [
                        'hidden' => [],
                        'structure' => [],
                        'columns' => [],
                        'data_source_type' => $existing_data_source_type,
                        'data_source_id' => $existing_data_source_id,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    data_table_storage::save_configuration($table_name, $column_preferences, $user_id, $existing_data_source_id);
                }
            }
        }

        // Apply column structure if preferences exist, otherwise use original
        if (!empty($column_preferences['structure'])) {
            $columns = self::apply_column_structure($original_columns, $column_preferences['structure']);
        } else {
            $columns = $original_columns;
        }

        $data = [
            "title" => "subscriptions",
            "description" => "",
            "items" => self::$items,
            "columns" => $columns,
            "available_fields" => self::$available_fields,
            "rows" => [
                'id' => 'subscription_id_' . $items['id'],
                'extra_parameters' => $htmx_oob_out,
            ],
            "just_body" => $just_body,
            "just_rows" => $just_rows,
            "just_table" => $just_table,
            "sort_column" => $sort_column,
            "sort_direction" => $sort_direction,
            "callback" => $callback,
            "table_name" => $table_name,
            "column_preferences" => $column_preferences,
            "show_data_source_selector"=>"true"
        ];

        // Add total count for pagination if provided
        if ($total_count !== null) {
            $data['total_count'] = $total_count;
        }
        $data2 = $data;
        $data2['items'] = array_slice($data2['items'], 0, 5);
        print_rr(input:$data2, label:'datad',full_params: true);
        if ($just_table) {
            print_rr('tcs returning just table', 'datad');
            if($include_column_manager) $data['include_column_manager'] = true;
            return Edge::render('data-table-structure', $data);
        }
        print_rr('tcs returning full table', 'datad');
        return Edge::render('data-table', $data);
    }



    public static function data_table_filter($p,$callback){
        $criteria = data_table::api_process_criteria($p);
        return $callback(criteria: $criteria);
    }

    /**
     * Simple table reload - just call the appropriate template/callback
     * Let the templates handle loading saved preferences
     */
    public static function reload_table($params) {
        // Remove column preference parameters from the callback params
        unset($params['hidden_columns'], $params['column_structure'], $params['action'], $params['column_id'], $params['field_name'], $params['source_column_id'], $params['target_column_id'], $params['column_order'], $params['column_name']);

        $criteria = self::api_process_criteria($params);
        $table_name = $params['table_name'] ?? '';
        $callback = $params['callback'] ?? '';

        // Get configuration to determine table type
        $user_id = data_table_storage::get_current_user_id();
        $config = data_table_storage::get_configuration($table_name, $user_id);
        $data_source_id = null;

        // Handle data source tables - call data-table-structure template directly
        if ($config && ($config['configuration']['data_source_type'] ?? '') === 'data_source') {
            $data_source_id = $config['data_source_id'] ?? $config['configuration']['data_source_id'] ?? null;

            return edge::render('data-table-structure', [
                'table_name' => $table_name,
                'data_source_id' => $data_source_id,
                'criteria' => $criteria,
                'just_table' => true,
                'include_column_manager' => true
            ]);
        }

        // Handle hardcoded tables - call the callback with just_table = true
        if (!empty($callback) && function_exists($callback)) {
            $criteria['just_table'] = true;
            return $callback($criteria);
        }

        // Fallback
        return "Table {$table_name} not found. Callback: {$callback} data source: {$data_source_id}";
    }

// Complex regeneration methods removed - using simple reload approach

// Complex helper methods removed - templates handle everything now

    public static function filter_db_schema(): array {
        $db = [];
        foreach (self::$table_structure['columns'] as $column) {
            // Use only 'fields' array
            if (isset($column['fields']) && is_array($column['fields'])) {
                foreach ($column['fields'] as $field) {
                    if (!empty($field)) $db[] = $field;
                }
            }
        }
        return $db;
    }

    public static function api_process_criteria($input) {
        print_rr($input, 'api_process_criteria');
        $cols = $criteria = [];

        // Handle pagination parameters
        if (isset($input['limit'])) {
            $criteria['limit'] = (int)$input['limit'];
        }
        if (isset($input['offset'])) {
            $criteria['offset'] = (int)$input['offset'];
        }
        if (isset($input['page'])) {
            $criteria['page'] = (int)$input['page'];
        }
        if (isset($input['pagination_mode'])) {
            $criteria['pagination_mode'] = $input['pagination_mode'];
        }

        // Handle search terms
        if (isset($input['search_terms']) && !empty($input['search_terms'])) {
            $criteria['search'] = $input['search_terms'];
        }

        // Handle sorting
        if (isset($input['sort_column']) && !empty($input['sort_column'])) {
            $criteria['order_by'] = $input['sort_column'];
            $criteria['order_direction'] = $input['sort_direction'] ?? 'asc';
        }

        if (isset($input['column'])) {
            foreach ($input['column'] as $column => $value) {
                $value = $value['multi'];
                if (empty($value)) continue;
                print_rr($column, ' val: ' . $value);

                $col_parts = explode("_", $column, 2);
                $table = $col_parts[0];
                $column_name = $col_parts[1];
                if (strpos($value, ',')) {
                    $value_parts = explode(",", $value);
                    foreach ($value_parts as $value_b) {
                        $value_b = trim($value_b);
                        $cols['OR'][] = ["{$table}.{$column_name}",'=', $value_b];
                    }
                } else {
                    $cols["{$table}.{$column_name}"] = ['=', $value];
                }
            }
            if (count($cols) > 0) $criteria["where"] = $cols;
        }
        if (isset($input['search_terms']) && $input['search_terms'] != '') $criteria["search"] = $input['search_terms'];
        if (isset($input['order_by'])) {
            $criteria["sort_column"] = $input['order_by'];
            if (isset($input['order_direction'])) $criteria["sort_direction"] = $input['order_direction'];
        }
        return $criteria;
    }



    public static function build_column_filters(): array {
        $columns = self::process_auto_filters(self::$table_structure['columns']);
        $dsucpunt = 0;
        foreach ($columns as $key => $col) {
            $count = 1;
            $dsucpunt++;
            $first_field = $col['fields'][0] ?? '';
            $col_field = str_replace('_', '.', $first_field, $count);
            if (is_string($col_field)) $columns[$key]['selected'] = $criteria['where'][$col_field][1] ?? $col['label'];
            //$columns[$key]['selected'] = $criteria['where'][$col_field][1] ?? $col['label'];
        }
        print_rr($columns, 'build column filters end');
        return $columns;
    }

    public static function process_auto_filters(): array {
        $columns = self::$table_structure['columns'];
        foreach ($columns as $key => $col) {
            $first_field = $col['fields'][0] ?? '';
            $filter_criteria = [
                "order_by" => $first_field,
                'limit' => $col['filter_limit'] ?? 10
            ];
            if (isset($col['auto_filter']) && is_array($col['filter_data']) && count($col['filter_data']) > 0) {
                $filter_assoc = array_combine($col['filter_data'], $col['filter_data']);
                $columns[$key]['filters'] = $filter_assoc;
                unset($columns[$key]['filter_data']);
            }
        }
        return $columns;
    }


    public static function process_string_replacements(): void {
        foreach (self::$items as &$item) {
            foreach (self::$replacements as $field => $replacements) {
                if (is_string($item[$field])) {
                    $item[$field] = strtr($item[$field], $replacements);
                }
            }
            //$item[$field] = strtr($item[$field], $replacements);
        }
        unset($item);
    }

    public static function process_column_filters() {
        foreach (self::$table_structure['columns'] as $key => $col) {
            if (isset($col['string_replacements'])) self::process_string_replacements();
            if (isset($col['auto_filter'])) {
                if (isset($col['string_replacements'])) {
                    foreach (self::$table_structure['columns'][$key]['filter_data'] as &$item) {
                        if (is_string($item)) {
                            $item = strtr($item, $col['string_replacements']);
                        }
                    }
                }
            }
        }
    }

    /**
     * Apply column structure based on user preferences
     * This handles column ordering, field combinations, and visibility
     */
    public static function apply_column_structure(array $original_columns, array $structure): array {
        if (empty($structure)) {
            return $original_columns;
        }

        $processed_columns = [];
        $original_by_field = [];

        // Index original columns by their fields for lookup
        foreach ($original_columns as $column) {
            // Use fields array if available, otherwise fall back to field for legacy columns
            $column_fields = $column['fields'] ?? (is_array($column['field']) ? $column['field'] : [$column['field']]);
            foreach ($column_fields as $field) {
                if (!empty($field)) {
                    $original_by_field[$field] = $column;
                }
            }
        }

        // Process each column in the structure
        foreach ($structure as $struct_col) {
            if (!$struct_col['visible']) {
                continue; // Skip hidden columns
            }

            // Create new column based on structure
            // Use only fields array - no field property
            $fields_array = $struct_col['fields'] ?? [];
            $new_column = [
                'label' => $struct_col['label'],
                'fields' => $fields_array,
                'filter' => $struct_col['filter'] ?? false,
                'extra_parameters' => ''
            ];

            // Copy any additional properties from original column
            $first_field = $struct_col['fields'][0] ?? '';
            if (isset($original_by_field[$first_field])) {
                $original = $original_by_field[$first_field];
                $new_column['replacements'] = $original['replacements'] ?? null;
                $new_column['content'] = $original['content'] ?? null;
                $new_column['auto_filter'] = $original['auto_filter'] ?? null;
                $new_column['filter_data'] = $original['filter_data'] ?? null;
            }

            $processed_columns[] = $new_column;
        }

        // Add any original columns that weren't in the structure
        foreach ($original_columns as $original) {
            // Use fields array if available, otherwise fall back to field for legacy columns
            $original_fields = $original['fields'] ?? (is_array($original['field']) ? $original['field'] : [$original['field']]);
            $found_in_structure = false;

            foreach ($structure as $struct_col) {
                if (array_intersect($original_fields, $struct_col['fields'])) {
                    $found_in_structure = true;
                    break;
                }
            }

            if (!$found_in_structure) {
                $processed_columns[] = $original;
            }
        }

        return $processed_columns;
    }

    /**
     * Get column structure from data source
     *
     * @param int $data_source_id Data source ID
     * @return array Column structure array
     */
    private static function get_data_source_columns(int $data_source_id): array {
        try {
            $data_source = database::table('autobooks_data_sources')
                ->where('id', $data_source_id)
                ->first();

            if (!$data_source) {
                return [];
            }

            $selected_columns = json_decode($data_source['selected_columns'], true);
            $column_aliases = json_decode($data_source['column_aliases'], true);

            if (!is_array($selected_columns)) {
                return [];
            }

            $columns = [];
            $column_index = 0;

            foreach ($selected_columns as $table => $table_columns) {
                if (is_array($table_columns)) {
                    foreach ($table_columns as $column) {
                        $column_id = 'col_' . $column_index . '_' . md5($table . '.' . $column);
                        $display_name = $column_aliases[$column] ?? $column;

                        $columns[] = [
                            'id' => $column_id,
                            'label' => ucwords(str_replace('_', ' ', $display_name)),
                            'fields' => [$table . '_' . $column],
                            'filter' => true,
                            'visible' => true
                        ];
                        $column_index++;
                    }
                }
            }

            return $columns;

        } catch (Exception $e) {
            return [];
        }
    }
}
