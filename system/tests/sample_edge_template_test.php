<?php
/**
 * Sample Edge Template Test
 * This test demonstrates how to test Edge template functionality
 */

use edge\Edge;

try {
    echo "Starting Edge template test...\n";
    
    // Test 1: Check if Edge class is available
    if (!class_exists('edge\Edge')) {
        throw new Exception('Edge class not available');
    }
    echo "✓ Edge class loaded\n";
    
    // Test 2: Test basic variable rendering
    $test_template = '{{ $greeting }} {{ $name }}!';
    $temp_file = FS_SYS_VIEWS . '/system/temp_edge_test.edge.php';
    
    file_put_contents($temp_file, $test_template);
    
    try {
        $data = ['greeting' => 'Hello', 'name' => 'World'];
        $rendered = Edge::render('system/temp_edge_test', $data);
        
        if (trim($rendered) === 'Hello World!') {
            echo "✓ Basic variable rendering works\n";
        } else {
            throw new Exception("Expected 'Hello World!', got: " . trim($rendered));
        }
    } finally {
        // Clean up
        if (file_exists($temp_file)) {
            unlink($temp_file);
        }
    }
    
    // Test 3: Test conditional rendering
    $conditional_template = '@if($show_message){{ $message }}@endif';
    $temp_file2 = FS_SYS_VIEWS . '/system/temp_edge_conditional_test.edge.php';
    
    file_put_contents($temp_file2, $conditional_template);
    
    try {
        // Test with condition true
        $data = ['show_message' => true, 'message' => 'Visible'];
        $rendered = Edge::render('system/temp_edge_conditional_test', $data);
        
        if (trim($rendered) === 'Visible') {
            echo "✓ Conditional rendering (true) works\n";
        } else {
            throw new Exception("Expected 'Visible', got: " . trim($rendered));
        }
        
        // Test with condition false
        $data = ['show_message' => false, 'message' => 'Hidden'];
        $rendered = Edge::render('system/temp_edge_conditional_test', $data);
        
        if (trim($rendered) === '') {
            echo "✓ Conditional rendering (false) works\n";
        } else {
            throw new Exception("Expected empty string, got: " . trim($rendered));
        }
    } finally {
        // Clean up
        if (file_exists($temp_file2)) {
            unlink($temp_file2);
        }
    }
    
    // Test 4: Test component rendering (if components exist)
    try {
        $component_template = '<x-forms-button label="Test Button" />';
        $temp_file3 = FS_SYS_VIEWS . '/system/temp_edge_component_test.edge.php';
        
        file_put_contents($temp_file3, $component_template);
        
        $rendered = Edge::render('system/temp_edge_component_test', []);
        
        if (strpos($rendered, 'Test Button') !== false) {
            echo "✓ Component rendering works\n";
        } else {
            echo "⚠ Component rendering test skipped (component may not exist)\n";
        }
        
        if (file_exists($temp_file3)) {
            unlink($temp_file3);
        }
    } catch (Exception $e) {
        echo "⚠ Component test skipped: " . $e->getMessage() . "\n";
    }
    
    echo "Edge template test completed successfully!\n";
    return true;
    
} catch (Exception $e) {
    echo "✗ Edge template test failed: " . $e->getMessage() . "\n";
    return false;
}
?>
