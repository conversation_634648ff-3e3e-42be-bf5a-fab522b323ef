<?php
require_once 'system/autoloader.php';

use system\unified_field_mapper;
use system\unified_field_definitions;

echo "Testing Space/Underscore Field Matching\n";
echo "=====================================\n\n";

// Test cases for space/underscore matching
$test_cases = [
    // Test underscore to space matching
    ['input' => 'end_date', 'should_match' => 'end date'],
    ['input' => 'start_date', 'should_match' => 'start date'],
    ['input' => 'customer_name', 'should_match' => 'customer name'],
    ['input' => 'product_id', 'should_match' => 'product id'],
    
    // Test space to underscore matching
    ['input' => 'end date', 'should_match' => 'end_date'],
    ['input' => 'start date', 'should_match' => 'start_date'],
    ['input' => 'customer name', 'should_match' => 'customer_name'],
    ['input' => 'product id', 'should_match' => 'product_id'],
    
    // Test mixed cases
    ['input' => 'End_Date', 'should_match' => 'end date'],
    ['input' => 'START DATE', 'should_match' => 'start_date'],
    ['input' => 'Customer_Name', 'should_match' => 'customer name'],
];

echo "1. Testing unified_field_definitions::find_field_by_pattern()\n";
echo "-----------------------------------------------------------\n";

foreach ($test_cases as $i => $test) {
    $result = unified_field_definitions::find_field_by_pattern($test['input']);
    
    echo sprintf("Test %d: Input '%s' -> Result: %s\n", 
        $i + 1, 
        $test['input'], 
        $result ? $result : 'No match'
    );
    
    if ($result) {
        $field_def = unified_field_definitions::get_field($result);
        if ($field_def && isset($field_def['patterns'])) {
            $patterns = array_map('strtolower', $field_def['patterns']);
            $expected_variations = [
                strtolower($test['should_match']),
                str_replace('_', ' ', strtolower($test['should_match'])),
                str_replace(' ', '_', strtolower($test['should_match']))
            ];
            $expected_variations = array_unique($expected_variations);
            
            $match_found = false;
            foreach ($expected_variations as $expected) {
                if (in_array($expected, $patterns)) {
                    $match_found = true;
                    break;
                }
            }
            
            echo sprintf("  -> Expected pattern variations: %s\n", implode(', ', $expected_variations));
            echo sprintf("  -> Field patterns: %s\n", implode(', ', array_slice($patterns, 0, 5)));
            echo sprintf("  -> Match validation: %s\n", $match_found ? 'PASS' : 'FAIL');
        }
    }
    echo "\n";
}

echo "\n2. Testing unified_field_mapper::find_best_field_match()\n";
echo "------------------------------------------------------\n";

foreach ($test_cases as $i => $test) {
    $result = unified_field_mapper::find_best_field_match($test['input']);
    
    echo sprintf("Test %d: Input '%s' -> Result: %s (Confidence: %d%%)\n", 
        $i + 1, 
        $test['input'], 
        $result['field'] ?? 'No match',
        $result['confidence'] ?? 0
    );
    
    if (isset($result['field'])) {
        echo sprintf("  -> Matched field: %s\n", $result['field']);
        echo sprintf("  -> Confidence: %d%%\n", $result['confidence']);
        echo sprintf("  -> Expected high confidence (90%+): %s\n", 
            $result['confidence'] >= 90 ? 'PASS' : 'FAIL'
        );
    }
    echo "\n";
}

echo "\n3. Testing specific field definitions\n";
echo "------------------------------------\n";

// Get some actual field definitions to test with
$all_fields = unified_field_definitions::get_all_fields();
$sample_fields = array_slice($all_fields, 0, 5, true);

foreach ($sample_fields as $field_name => $definition) {
    echo sprintf("Field: %s\n", $field_name);
    echo sprintf("Patterns: %s\n", implode(', ', array_slice($definition['patterns'], 0, 3)));
    
    // Test the first pattern with space/underscore variations
    if (!empty($definition['patterns'])) {
        $original_pattern = $definition['patterns'][0];
        $test_variations = [
            str_replace('_', ' ', $original_pattern),
            str_replace(' ', '_', $original_pattern),
            strtoupper($original_pattern),
            ucwords(str_replace('_', ' ', $original_pattern))
        ];
        
        foreach ($test_variations as $variation) {
            if ($variation !== $original_pattern) {
                $match_result = unified_field_definitions::find_field_by_pattern($variation);
                echo sprintf("  Test '%s' -> %s\n", 
                    $variation, 
                    $match_result === $field_name ? 'MATCH' : 'NO MATCH'
                );
            }
        }
    }
    echo "\n";
}
