<?php
/**
 * Test script to verify unified field information is preserved in final configuration
 * Access via browser: http://localhost/autobooks/test_unified_field_preservation.php
 */

header('Content-Type: text/plain');

echo "=== Testing Unified Field Information Preservation ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the minimal startup sequence
    require_once 'system/startup_sequence_minimal.php';
    
    echo "1. System initialized successfully\n\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    
    // Check if table exists
    if (!system\database::tableExists($table_name)) {
        echo "❌ Table '{$table_name}' does not exist. Please import SketchUp data first.\n";
        exit(1);
    }
    
    echo "2. Testing data table generator with unified field preservation...\n";
    
    $options = [
        'use_intelligent_column_selection' => true,
        'use_intelligent_naming' => true,
        'table_name' => $table_name,
        'max_visible_columns' => 8
    ];
    
    $config_result = system\data_table_generator::generate_table_config($table_name, [], $options);
    
    if (isset($config_result['error'])) {
        echo "❌ Data table generator failed: " . $config_result['error'] . "\n";
        exit(1);
    }
    
    $config = $config_result['config'];
    
    echo "✅ Configuration generated successfully\n";
    echo "- Visible columns: " . count($config['columns']) . "\n";
    echo "- Available fields: " . count($config['available_fields']) . "\n\n";
    
    echo "3. Analyzing unified field information in visible columns...\n";
    
    $matched_columns = 0;
    $unmatched_columns = 0;
    $columns_with_unified_info = 0;
    
    foreach ($config['columns'] as $i => $column) {
        $field_name = $column['field'];
        $label = $column['label'];
        $has_unified_field = !empty($column['unified_field']);
        $confidence = $column['confidence'] ?? 0;
        
        if ($has_unified_field) {
            $matched_columns++;
            $columns_with_unified_info++;
            echo "  " . ($i + 1) . ". {$field_name} -> {$column['unified_field']} ({$label}, confidence: {$confidence})\n";
        } else {
            $unmatched_columns++;
            echo "  " . ($i + 1) . ". {$field_name} (no unified field) ({$label})\n";
        }
    }
    
    echo "\nUnified field preservation analysis:\n";
    echo "- Columns with unified field info: {$columns_with_unified_info}\n";
    echo "- Matched columns: {$matched_columns}\n";
    echo "- Unmatched columns: {$unmatched_columns}\n";
    
    if ($columns_with_unified_info > 0) {
        echo "✅ Unified field information is being preserved!\n";
    } else {
        echo "❌ Unified field information is NOT being preserved\n";
    }
    
    // Test the actual unified field matching to compare
    echo "\n4. Comparing with direct unified field matching...\n";
    
    $columns_info = system\database::getColumnInfo($table_name);
    $all_columns = array_column($columns_info, 'Field');
    $visible_column_names = array_column($config['columns'], 'field');
    
    echo "Testing unified field matching for visible columns...\n";
    $suggestions = system\unified_field_mapper::suggest_field_mappings($visible_column_names);
    
    $direct_matched = 0;
    $direct_unmatched = 0;
    
    foreach ($visible_column_names as $column_name) {
        if (isset($suggestions[$column_name])) {
            $direct_matched++;
            $suggestion = $suggestions[$column_name];
            echo "  Direct match: {$column_name} -> {$suggestion['field_name']} (confidence: {$suggestion['confidence']})\n";
        } else {
            $direct_unmatched++;
            echo "  Direct unmatched: {$column_name}\n";
        }
    }
    
    echo "\nDirect matching results:\n";
    echo "- Direct matched: {$direct_matched}\n";
    echo "- Direct unmatched: {$direct_unmatched}\n";
    
    // Compare the results
    echo "\n5. Comparison of preservation vs direct matching...\n";
    
    if ($columns_with_unified_info == $direct_matched) {
        echo "✅ Perfect match! Unified field preservation is working correctly\n";
        echo "✅ All matched columns have their unified field information preserved\n";
    } elseif ($columns_with_unified_info > 0 && $columns_with_unified_info < $direct_matched) {
        echo "⚠️ Partial preservation: {$columns_with_unified_info} preserved out of {$direct_matched} matched\n";
        echo "Some unified field information may be getting lost in the process\n";
    } elseif ($columns_with_unified_info == 0 && $direct_matched > 0) {
        echo "❌ No preservation: 0 preserved out of {$direct_matched} matched\n";
        echo "Unified field information is being lost completely\n";
    } else {
        echo "ℹ️ Unexpected result: preserved={$columns_with_unified_info}, direct={$direct_matched}\n";
    }
    
    // Test the column selection logic
    echo "\n6. Testing column selection logic...\n";
    
    $should_be_visible = 0;
    $should_be_hidden = 0;
    
    foreach ($suggestions as $field => $suggestion) {
        $should_show = system\unified_field_definitions::should_show_by_default($suggestion['field_name']);
        if ($should_show) {
            $should_be_visible++;
        } else {
            $should_be_hidden++;
        }
    }
    
    echo "Column selection expectations:\n";
    echo "- Should be visible (matched + show_by_default): {$should_be_visible}\n";
    echo "- Should be hidden (matched + hide_by_default): {$should_be_hidden}\n";
    echo "- Actually visible: " . count($config['columns']) . "\n";
    echo "- Actually available: " . count($config['available_fields']) . "\n";
    
    $visible_diff = abs(count($config['columns']) - $should_be_visible);
    if ($visible_diff <= 2) { // Allow for system columns like 'id'
        echo "✅ Column selection is working correctly\n";
    } else {
        echo "⚠️ Column selection may need adjustment (difference: {$visible_diff})\n";
    }
    
    echo "\n=== Test Summary ===\n";
    
    if ($columns_with_unified_info == $direct_matched && $visible_diff <= 2) {
        echo "✅ All fixes are working perfectly!\n";
        echo "✅ Unified field information is preserved in final configuration\n";
        echo "✅ Column selection is showing appropriate fields\n";
        echo "✅ The SketchUp view should now display properly with unified field labels\n";
        
        echo "\nExpected user experience:\n";
        echo "- Clean table with ~" . count($config['columns']) . " relevant business columns\n";
        echo "- Unified field labels (e.g., 'Company Name' instead of 'end_customer_name')\n";
        echo "- ~" . count($config['available_fields']) . " additional fields available in column manager\n";
        echo "- No more technical field clutter in the main view\n";
    } else {
        echo "⚠️ Some issues remain:\n";
        if ($columns_with_unified_info != $direct_matched) {
            echo "- Unified field preservation needs work\n";
        }
        if ($visible_diff > 2) {
            echo "- Column selection logic needs adjustment\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Test ===\n";
?>
