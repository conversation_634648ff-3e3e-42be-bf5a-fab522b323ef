<?php
require_once 'system/autoloader.php';
use system\unified_field_definitions;

echo 'Testing field definitions loading...' . PHP_EOL;

// Get all fields
$all_fields = unified_field_definitions::get_all_fields();
echo 'Total fields loaded: ' . count($all_fields) . PHP_EOL;

// Check if company_name field has matching enabled
if (isset($all_fields['company_name'])) {
    $company_field = $all_fields['company_name'];
    echo 'Company name field found' . PHP_EOL;
    echo 'Matching enabled: ' . ($company_field['matching']['enabled'] ? 'YES' : 'NO') . PHP_EOL;
    echo 'Priority: ' . ($company_field['matching']['priority'] ?? 'not set') . PHP_EOL;
} else {
    echo 'Company name field NOT found' . PHP_EOL;
}

// Get matching fields
$matching_fields = unified_field_definitions::get_matching_fields();
echo 'Fields enabled for matching: ' . count($matching_fields) . PHP_EOL;
foreach ($matching_fields as $name => $field) {
    echo '  - ' . $name . ' (priority: ' . ($field['matching']['priority'] ?? 'not set') . ')' . PHP_EOL;
}

// Check database directly
echo PHP_EOL . 'Checking database directly...' . PHP_EOL;
try {
    $custom_fields = database::table('autobooks_unified_field_definitions')
        ->where('is_active', 1)
        ->get();
    
    echo 'Custom field definitions in database: ' . count($custom_fields) . PHP_EOL;
    foreach ($custom_fields as $custom_field) {
        $field_data = json_decode($custom_field['field_definition'], true);
        echo '  - ' . $custom_field['field_name'] . ' (matching enabled: ' . 
             ($field_data['matching']['enabled'] ? 'YES' : 'NO') . ')' . PHP_EOL;
    }
} catch (Exception $e) {
    echo 'Error checking database: ' . $e->getMessage() . PHP_EOL;
}
