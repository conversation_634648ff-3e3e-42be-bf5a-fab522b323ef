<?php
require_once 'system/classes/database.class.php';
require_once 'system/classes/data_source_manager.class.php';

// Get a data source that has column aliases
$db = \system\database::table('autobooks_data_sources');
$data_sources = $db->where('column_aliases', '!=', '')->where('column_aliases', '!=', 'null')->limit(3)->get();

echo "Found " . count($data_sources) . " data sources with column aliases:\n";

foreach ($data_sources as $ds) {
    echo "Data Source ID: " . $ds['id'] . "\n";
    echo "Name: " . $ds['name'] . "\n";
    echo "Column Aliases: " . $ds['column_aliases'] . "\n";
    echo "---\n";
}

// Test the column_selection_fragment function with a data source that has aliases
if (!empty($data_sources)) {
    $test_ds = $data_sources[0];
    echo "\nTesting column_selection_fragment with data source: " . $test_ds['name'] . "\n";
    
    // Parse the data source data
    $tables = json_decode($test_ds['tables'] ?? '[]', true) ?: [];
    $selected_columns = json_decode($test_ds['selected_columns'] ?? '[]', true) ?: [];
    $column_aliases = json_decode($test_ds['column_aliases'] ?? '{}', true) ?: [];
    $table_aliases = json_decode($test_ds['table_aliases'] ?? '{}', true) ?: [];
    $joins = json_decode($test_ds['joins'] ?? '[]', true) ?: [];
    
    echo "Tables: " . json_encode($tables) . "\n";
    echo "Selected Columns: " . json_encode($selected_columns) . "\n";
    echo "Column Aliases: " . json_encode($column_aliases) . "\n";
    echo "Table Aliases: " . json_encode($table_aliases) . "\n";
    
    // Test the function
    require_once 'system/api/data_sources.api.php';
    
    $params = [
        'selected_tables' => json_encode($tables),
        'selected_columns' => json_encode($selected_columns),
        'joins' => json_encode($joins),
        'table_aliases' => $table_aliases,
        'column_aliases' => $column_aliases
    ];
    
    echo "\nCalling column_selection_fragment...\n";
    $result = \api\data_sources\column_selection_fragment($params);
    
    // Check if the result contains the aliases
    if (strpos($result, 'column_aliases') !== false) {
        echo "SUCCESS: Result contains column_aliases references\n";
    } else {
        echo "ISSUE: Result does not contain column_aliases references\n";
    }
    
    // Look for specific alias values in the output
    foreach ($column_aliases as $column => $alias) {
        if (strpos($result, $alias) !== false) {
            echo "SUCCESS: Found alias '$alias' for column '$column' in output\n";
        } else {
            echo "ISSUE: Alias '$alias' for column '$column' not found in output\n";
        }
    }
}
