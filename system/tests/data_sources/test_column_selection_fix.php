<?php
/**
 * Test script to verify the column selection fix works correctly
 * Access via browser: http://localhost/autobooks/test_column_selection_fix.php
 */

header('Content-Type: text/plain');

echo "=== Testing Column Selection Fix ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the minimal startup sequence
    require_once 'system/startup_sequence_minimal.php';
    
    echo "1. System initialized successfully\n\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    
    echo "2. Testing column selection with SketchUp data...\n";
    
    // Check if the table exists
    $table_exists = system\database::tableExists($table_name);
    if (!$table_exists) {
        echo "❌ Table '{$table_name}' does not exist. Please import SketchUp data first.\n";
        exit(1);
    }

    echo "✅ Table '{$table_name}' exists\n";

    // Get table columns
    $columns_info = system\database::getColumnInfo($table_name);
    $column_names = array_column($columns_info, 'Field');
    
    echo "Total columns in table: " . count($column_names) . "\n";
    echo "Sample columns: " . implode(', ', array_slice($column_names, 0, 10)) . "...\n\n";
    
    // Test the data table generator with the fix
    echo "3. Testing data table generator with fixed column selection...\n";
    
    $criteria = ['limit' => 10];
    $options = [
        'use_intelligent_column_selection' => true,
        'use_intelligent_naming' => true,
        'table_name' => $table_name,
        'max_visible_columns' => 8
    ];
    
    $config_result = system\data_table_generator::generate_table_config($table_name, $criteria, $options);
    
    if (isset($config_result['error'])) {
        echo "❌ Table config generation failed: " . $config_result['error'] . "\n";
        exit(1);
    }
    
    $config = $config_result['config'];
    
    echo "✅ Table configuration generated successfully!\n";
    echo "- Visible columns: " . count($config['columns']) . "\n";
    echo "- Available fields: " . count($config['available_fields']) . "\n\n";
    
    // Analyze the visible columns
    echo "4. Analyzing visible columns...\n";
    
    $matched_visible = 0;
    $unmatched_visible = 0;
    $essential_visible = 0;
    
    foreach ($config['columns'] as $i => $column) {
        $field_name = $column['field'];
        $label = $column['label'];
        $is_essential = in_array($field_name, ['id']);
        
        if (!empty($column['unified_field'])) {
            $matched_visible++;
            echo "  " . ($i + 1) . ". {$field_name} -> {$column['unified_field']} ({$label})\n";
        } elseif ($is_essential) {
            $essential_visible++;
            echo "  " . ($i + 1) . ". {$field_name} (essential system column) ({$label})\n";
        } else {
            $unmatched_visible++;
            echo "  " . ($i + 1) . ". {$field_name} (unmatched - should not be visible!) ({$label})\n";
        }
    }
    
    echo "\nVisible column analysis:\n";
    echo "- Matched columns (should be visible): {$matched_visible}\n";
    echo "- Essential columns (should be visible): {$essential_visible}\n";
    echo "- Unmatched columns (should NOT be visible): {$unmatched_visible}\n";
    
    if ($unmatched_visible == 0) {
        echo "✅ Column selection fix is working correctly!\n";
        echo "✅ Only matched and essential columns are visible\n";
    } else {
        echo "❌ Column selection fix needs more work\n";
        echo "❌ {$unmatched_visible} unmatched columns are still visible\n";
    }
    
    // Analyze available fields
    echo "\n5. Analyzing available fields...\n";
    
    $total_fields = count($column_names);
    $visible_fields = count($config['columns']);
    $available_fields = count($config['available_fields']);
    $expected_available = $total_fields - $visible_fields;
    
    echo "Field distribution:\n";
    echo "- Total fields in table: {$total_fields}\n";
    echo "- Visible fields: {$visible_fields}\n";
    echo "- Available fields: {$available_fields}\n";
    echo "- Expected available: {$expected_available}\n";
    
    if ($available_fields >= $expected_available - 2 && $available_fields <= $expected_available + 2) {
        echo "✅ Available fields count looks correct\n";
    } else {
        echo "⚠️ Available fields count might be off\n";
    }
    
    // Show some available fields
    echo "\nSample available fields (first 10):\n";
    foreach (array_slice($config['available_fields'], 0, 10) as $i => $field) {
        echo "  " . ($i + 1) . ". {$field}\n";
    }
    
    if (count($config['available_fields']) > 10) {
        echo "  ... and " . (count($config['available_fields']) - 10) . " more fields\n";
    }
    
    // Test unified field matching statistics
    echo "\n6. Unified field matching statistics...\n";
    
    // Get unified field suggestions for all columns
    $suggestions = system\unified_field_mapper::suggest_field_mappings($column_names);
    
    $total_columns = count($column_names);
    $matched_columns = count($suggestions);
    $unmatched_columns = $total_columns - $matched_columns;
    
    echo "Unified field matching results:\n";
    echo "- Total columns: {$total_columns}\n";
    echo "- Matched columns: {$matched_columns}\n";
    echo "- Unmatched columns: {$unmatched_columns}\n";
    echo "- Match rate: " . round(($matched_columns / $total_columns) * 100, 1) . "%\n";
    
    // Count how many matched fields should be shown by default
    $show_by_default_count = 0;
    $hide_by_default_count = 0;
    
    foreach ($suggestions as $field => $suggestion) {
        $should_show = system\unified_field_definitions::should_show_by_default($suggestion['field_name']);
        if ($should_show) {
            $show_by_default_count++;
        } else {
            $hide_by_default_count++;
        }
    }
    
    echo "\nDisplay preferences for matched fields:\n";
    echo "- Show by default: {$show_by_default_count}\n";
    echo "- Hide by default: {$hide_by_default_count}\n";
    
    $expected_visible = min($show_by_default_count + 1, 8); // +1 for 'id', max 8
    
    echo "- Expected visible columns: ~{$expected_visible}\n";
    echo "- Actual visible columns: {$visible_fields}\n";
    
    if (abs($visible_fields - $expected_visible) <= 2) {
        echo "✅ Visible column count matches expectations\n";
    } else {
        echo "⚠️ Visible column count differs from expectations\n";
    }
    
    echo "\n=== Test Complete ===\n";
    
    if ($unmatched_visible == 0 && abs($visible_fields - $expected_visible) <= 2) {
        echo "✅ Column selection fix is working perfectly!\n";
        echo "✅ Only matched fields with 'show_by_default=true' are visible\n";
        echo "✅ Unmatched fields are properly hidden\n";
        echo "✅ Hidden fields are available in the column manager\n";
        
        echo "\nThe SketchUp view should now show a clean, focused table with:\n";
        echo "- ~{$expected_visible} core business columns initially visible\n";
        echo "- ~{$available_fields} additional fields available for manual addition\n";
        echo "- Unified field labels instead of raw column names\n";
    } else {
        echo "⚠️ Column selection fix may need additional adjustments\n";
        echo "Check the analysis above for specific issues\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Test ===\n";
?>
