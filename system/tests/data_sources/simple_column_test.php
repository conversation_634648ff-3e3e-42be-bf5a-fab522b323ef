<?php
/**
 * Simple test to verify column selection fix
 * Access via browser: http://localhost/autobooks/simple_column_test.php
 */

header('Content-Type: text/plain');

echo "=== Simple Column Selection Test ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Include the minimal startup sequence
    require_once 'system/startup_sequence_minimal.php';
    
    echo "1. System initialized successfully\n\n";
    
    $table_name = 'autobooks_import_sketchup_data';
    
    // Check if table exists
    if (!system\database::tableExists($table_name)) {
        echo "❌ Table '{$table_name}' does not exist. Please import SketchUp data first.\n";
        exit(1);
    }
    
    echo "2. Testing column selection logic...\n";
    
    // Get all columns from the table
    $columns_info = system\database::getColumnInfo($table_name);
    $all_columns = array_column($columns_info, 'Field');
    
    echo "Total columns in table: " . count($all_columns) . "\n";
    
    // Test unified field matching
    $suggestions = system\unified_field_mapper::suggest_field_mappings($all_columns);
    
    echo "Unified field matches: " . count($suggestions) . "\n";
    echo "Unmatched columns: " . (count($all_columns) - count($suggestions)) . "\n\n";
    
    // Simulate the fixed column selection logic
    echo "3. Simulating fixed column selection logic...\n";
    
    $visible_columns = [];
    $hidden_columns = [];
    
    foreach ($all_columns as $column_name) {
        // Skip system columns that should always be hidden
        if (in_array($column_name, ['created_at', 'updated_at', 'data_hash'])) {
            $hidden_columns[] = $column_name;
            continue;
        }
        
        $should_show = false; // Default to hiding unmatched columns (THE FIX)
        
        if (isset($suggestions[$column_name])) {
            // Column has unified field match
            $suggestion = $suggestions[$column_name];
            $unified_field_name = $suggestion['field_name'];
            $should_show = system\unified_field_definitions::should_show_by_default($unified_field_name);
            
            echo "  {$column_name} -> {$unified_field_name} (show: " . ($should_show ? 'YES' : 'NO') . ")\n";
        } else {
            // Column has no unified field match
            $essential_columns = ['id']; // Only show essential system columns
            $should_show = in_array($column_name, $essential_columns);
            
            echo "  {$column_name} (unmatched, show: " . ($should_show ? 'YES as essential' : 'NO') . ")\n";
        }
        
        if ($should_show) {
            $visible_columns[] = $column_name;
        } else {
            $hidden_columns[] = $column_name;
        }
    }
    
    echo "\n4. Results of fixed column selection:\n";
    echo "Visible columns: " . count($visible_columns) . "\n";
    echo "Hidden columns: " . count($hidden_columns) . "\n\n";
    
    echo "Visible columns (should be ~8 core business fields):\n";
    foreach ($visible_columns as $i => $column) {
        echo "  " . ($i + 1) . ". {$column}\n";
    }
    
    echo "\nFirst 10 hidden columns (should include technical fields):\n";
    foreach (array_slice($hidden_columns, 0, 10) as $i => $column) {
        echo "  " . ($i + 1) . ". {$column}\n";
    }
    
    if (count($hidden_columns) > 10) {
        echo "  ... and " . (count($hidden_columns) - 10) . " more hidden columns\n";
    }
    
    // Test the actual data table generator
    echo "\n5. Testing actual data table generator...\n";
    
    $options = [
        'use_intelligent_column_selection' => true,
        'use_intelligent_naming' => true,
        'table_name' => $table_name,
        'max_visible_columns' => 8
    ];
    
    $config_result = system\data_table_generator::generate_table_config($table_name, [], $options);
    
    if (isset($config_result['error'])) {
        echo "❌ Data table generator failed: " . $config_result['error'] . "\n";
    } else {
        $config = $config_result['config'];
        $actual_visible = count($config['columns']);
        $actual_available = count($config['available_fields'] ?? []);
        
        echo "✅ Data table generator succeeded:\n";
        echo "  Actual visible columns: {$actual_visible}\n";
        echo "  Actual available fields: {$actual_available}\n";
        
        // Compare with our simulation
        $expected_visible = count($visible_columns);
        $expected_hidden = count($hidden_columns);
        
        echo "\nComparison:\n";
        echo "  Expected visible: {$expected_visible}\n";
        echo "  Actual visible: {$actual_visible}\n";
        echo "  Expected hidden: {$expected_hidden}\n";
        echo "  Actual available: {$actual_available}\n";
        
        if (abs($actual_visible - $expected_visible) <= 2) {
            echo "✅ Visible column count matches expectations!\n";
        } else {
            echo "⚠️ Visible column count differs from expectations\n";
        }
        
        if (abs($actual_available - $expected_hidden) <= 5) {
            echo "✅ Available field count matches expectations!\n";
        } else {
            echo "⚠️ Available field count differs from expectations\n";
        }
    }
    
    echo "\n=== Test Summary ===\n";
    
    if (count($visible_columns) <= 10 && count($hidden_columns) >= 40) {
        echo "✅ Column selection fix is working correctly!\n";
        echo "✅ Only matched fields with 'show_by_default=true' are visible\n";
        echo "✅ Unmatched fields are properly hidden by default\n";
        echo "✅ Hidden fields should be available in column manager\n";
        
        echo "\nExpected user experience:\n";
        echo "- Clean, focused table with ~{$actual_visible} relevant columns\n";
        echo "- ~{$actual_available} additional fields available for manual addition\n";
        echo "- No more overwhelming technical field clutter\n";
    } else {
        echo "⚠️ Column selection may need further adjustment\n";
        echo "- Too many visible columns: " . count($visible_columns) . " (should be ~8)\n";
        echo "- Too few hidden columns: " . count($hidden_columns) . " (should be ~50)\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== End of Test ===\n";
?>
