<?php

/**
 * Simple test for field name normalization logic
 * Tests the normalize_field_name function logic without requiring full system
 */

function normalize_field_name(string $field_name): array {
    $normalized = strtolower(trim($field_name));
    
    // Create variations: original, with spaces, with underscores
    $variations = [
        $normalized,
        str_replace('_', ' ', $normalized),  // underscores to spaces
        str_replace(' ', '_', $normalized)   // spaces to underscores
    ];
    
    // Remove duplicates and return
    return array_unique($variations);
}

function test_normalization() {
    echo "Testing Field Name Normalization\n";
    echo "===============================\n\n";
    
    $test_cases = [
        'end_date',
        'end date',
        'End_Date',
        'END DATE',
        'customer_name',
        'customer name',
        'product_id',
        'product id',
        'start_date_time',
        'start date time',
        'mixed_Case_Example',
        'Mixed Case Example'
    ];
    
    foreach ($test_cases as $test_case) {
        $variations = normalize_field_name($test_case);
        echo sprintf("Input: '%s'\n", $test_case);
        echo sprintf("Variations: %s\n", implode(', ', $variations));
        
        // Test if variations would match expected patterns
        $expected_underscore = str_replace(' ', '_', strtolower($test_case));
        $expected_space = str_replace('_', ' ', strtolower($test_case));
        
        $has_underscore = in_array($expected_underscore, $variations);
        $has_space = in_array($expected_space, $variations);
        
        echo sprintf("Contains underscore version ('%s'): %s\n", $expected_underscore, $has_underscore ? 'YES' : 'NO');
        echo sprintf("Contains space version ('%s'): %s\n", $expected_space, $has_space ? 'YES' : 'NO');
        echo "\n";
    }
}

function test_matching_logic() {
    echo "Testing Matching Logic\n";
    echo "=====================\n\n";
    
    $test_patterns = [
        ['input' => 'end_date', 'pattern' => 'end date'],
        ['input' => 'end date', 'pattern' => 'end_date'],
        ['input' => 'customer_name', 'pattern' => 'customer name'],
        ['input' => 'product id', 'pattern' => 'product_id'],
        ['input' => 'Start_Date', 'pattern' => 'start date'],
        ['input' => 'END DATE', 'pattern' => 'end_date']
    ];
    
    foreach ($test_patterns as $test) {
        $input_variations = normalize_field_name($test['input']);
        $pattern_variations = normalize_field_name($test['pattern']);
        
        $match = array_intersect($input_variations, $pattern_variations);
        
        echo sprintf("Input: '%s' vs Pattern: '%s'\n", $test['input'], $test['pattern']);
        echo sprintf("Input variations: %s\n", implode(', ', $input_variations));
        echo sprintf("Pattern variations: %s\n", implode(', ', $pattern_variations));
        echo sprintf("Match found: %s\n", !empty($match) ? 'YES (' . implode(', ', $match) . ')' : 'NO');
        echo "\n";
    }
}

// Run tests
test_normalization();
echo "\n" . str_repeat("=", 50) . "\n\n";
test_matching_logic();

echo "Test completed successfully!\n";
