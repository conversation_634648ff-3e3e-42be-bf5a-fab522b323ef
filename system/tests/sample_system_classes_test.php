<?php
/**
 * Sample System Classes Test
 * This test demonstrates how to test system classes and functionality
 */

try {
    echo "Starting system classes test...\n";
    
    // Test 1: Check core system classes
    $core_classes = [
        'system\router' => 'Router',
        'system\users' => 'Users',
        'system\startup_sequence' => 'Startup Sequence'
    ];
    
    foreach ($core_classes as $class => $name) {
        if (class_exists($class)) {
            echo "✓ $name class loaded\n";
        } else {
            echo "✗ $name class not found\n";
        }
    }
    
    // Test 2: Check important constants
    $constants = [
        'APP_ROOT' => 'Application root path',
        'FS_VIEWS' => 'Views filesystem path',
        'FS_SYS_VIEWS' => 'System views filesystem path',
        'DEBUG_MODE' => 'Debug mode flag',
        'DOC_ROOT' => 'Document root path'
    ];
    
    foreach ($constants as $const => $description) {
        if (defined($const)) {
            $value = constant($const);
            $display_value = is_bool($value) ? ($value ? 'true' : 'false') : $value;
            echo "✓ $description: $display_value\n";
        } else {
            echo "✗ $description not defined\n";
        }
    }
    
    // Test 3: Check if autoloader is working
    if (function_exists('spl_autoload_functions')) {
        $autoloaders = spl_autoload_functions();
        echo "✓ Autoloader functions registered: " . count($autoloaders) . "\n";
    } else {
        echo "⚠ SPL autoload functions not available\n";
    }
    
    // Test 4: Check session handling
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "✓ Session is active\n";
    } else {
        echo "⚠ Session not active\n";
    }
    
    // Test 5: Test user authentication system (if available)
    try {
        if (class_exists('system\users')) {
            // Don't actually check auth as it might redirect, just test class methods exist
            if (method_exists('system\users', 'checkAuth')) {
                echo "✓ User authentication methods available\n";
            } else {
                echo "⚠ User authentication methods not found\n";
            }
        }
    } catch (Exception $e) {
        echo "⚠ User system test skipped: " . $e->getMessage() . "\n";
    }
    
    // Test 6: Check database functions
    $db_functions = ['tep_db_query', 'tep_db_fetch_array', 'tep_db_num_rows'];
    foreach ($db_functions as $func) {
        if (function_exists($func)) {
            echo "✓ Database function '$func' available\n";
        } else {
            echo "✗ Database function '$func' not found\n";
        }
    }
    
    // Test 7: Check file system permissions
    $writable_dirs = [FS_TEMP];
    foreach ($writable_dirs as $dir) {
        if (is_writable($dir)) {
            echo "✓ Directory '$dir' is writable\n";
        } else {
            echo "⚠ Directory '$dir' is not writable\n";
        }
    }
    
    echo "System classes test completed successfully!\n";
    return true;
    
} catch (Exception $e) {
    echo "✗ System classes test failed: " . $e->getMessage() . "\n";
    return false;
}
?>
