<?php
/**
 * Test Hash Generation
 *
 * This script tests the hash generation logic by comparing old and new hashes
 * for a sample of records from the products_autodesk_catalog table.
 *
 * Usage: php test_hash_generation.php
 */

// Set up error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include necessary files
$doc_root = dirname(dirname(__DIR__));
require_once($doc_root . '/system/includes/application_top.php');

/**
 * Generate hash string from field values
 */
function generate_hash_string($row) {
    $hash_fields = [
        'offeringId',
        'intendedUsage_code', 
        'accessModel_code',
        'servicePlan_code',
        'connectivity_code',
        'term_code',
        'orderAction',
        'specialProgramDiscount_code',
        'fromQty',
        'toQty'
    ];
    
    $hash_values = [];
    foreach ($hash_fields as $field) {
        $value = isset($row[$field]) ? $row[$field] : '';
        if ($value === null) {
            $value = '';
        }
        $hash_values[] = $value;
    }
    
    return implode('', $hash_values);
}

/**
 * Generate unique hash from hash string
 */
function generate_unique_hash($hash_string) {
    return hash('crc32', $hash_string);
}

echo "Testing hash generation logic...\n\n";

try {
    // Get a sample of records to test
    $query = "SELECT id, unique_hash, hash_string, offeringId, intendedUsage_code, 
                     accessModel_code, servicePlan_code, connectivity_code, term_code, 
                     orderAction, specialProgramDiscount_code, fromQty, toQty 
              FROM products_autodesk_catalog 
              LIMIT 10";
    
    $result = tep_db_query($query);
    $test_count = 0;
    $matches = 0;
    $hash_string_matches = 0;
    $unique_hash_matches = 0;
    
    echo "Testing first 10 records:\n";
    echo str_repeat("-", 120) . "\n";
    printf("%-4s %-10s %-10s %-8s %-8s %-50s\n", "ID", "Old Hash", "New Hash", "H Match", "U Match", "Generated Hash String");
    echo str_repeat("-", 120) . "\n";
    
    while ($row = tep_db_fetch_array($result)) {
        $test_count++;
        
        // Generate new hash string and unique hash
        $new_hash_string = generate_hash_string($row);
        $new_unique_hash = generate_unique_hash($new_hash_string);
        
        $old_hash_string = $row['hash_string'];
        $old_unique_hash = $row['unique_hash'];
        
        // Check matches
        $hash_string_match = ($new_hash_string === $old_hash_string);
        $unique_hash_match = ($new_unique_hash === $old_unique_hash);
        $overall_match = $hash_string_match && $unique_hash_match;
        
        if ($hash_string_match) $hash_string_matches++;
        if ($unique_hash_match) $unique_hash_matches++;
        if ($overall_match) $matches++;
        
        printf("%-4s %-10s %-10s %-8s %-8s %-50s\n", 
            $row['id'],
            $old_unique_hash,
            $new_unique_hash,
            $hash_string_match ? 'YES' : 'NO',
            $unique_hash_match ? 'YES' : 'NO',
            substr($new_hash_string, 0, 47) . (strlen($new_hash_string) > 47 ? '...' : '')
        );
        
        // Show details for mismatches
        if (!$overall_match) {
            echo "    Old hash string: " . $old_hash_string . "\n";
            echo "    New hash string: " . $new_hash_string . "\n";
            if (!$hash_string_match) {
                echo "    Hash string differs!\n";
            }
            if (!$unique_hash_match) {
                echo "    Unique hash differs!\n";
            }
            echo "\n";
        }
    }
    
    echo str_repeat("-", 120) . "\n";
    echo "\nSummary:\n";
    echo "Total records tested: {$test_count}\n";
    echo "Hash string matches: {$hash_string_matches}/{$test_count}\n";
    echo "Unique hash matches: {$unique_hash_matches}/{$test_count}\n";
    echo "Overall matches: {$matches}/{$test_count}\n";
    
    if ($matches < $test_count) {
        echo "\nHash generation logic appears to have changed. Running the fix script is recommended.\n";
    } else {
        echo "\nAll hashes match. No fix needed.\n";
    }
    
    // Test with a specific example to show the field breakdown
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "Detailed breakdown for first record:\n";
    echo str_repeat("=", 80) . "\n";
    
    $query = "SELECT * FROM products_autodesk_catalog LIMIT 1";
    $result = tep_db_query($query);
    $row = tep_db_fetch_array($result);
    
    $hash_fields = [
        'offeringId',
        'intendedUsage_code', 
        'accessModel_code',
        'servicePlan_code',
        'connectivity_code',
        'term_code',
        'orderAction',
        'specialProgramDiscount_code',
        'fromQty',
        'toQty'
    ];
    
    echo "Field values used for hash generation:\n";
    foreach ($hash_fields as $field) {
        $value = isset($row[$field]) ? $row[$field] : '';
        if ($value === null) $value = '';
        echo "  {$field}: '{$value}'\n";
    }
    
    $generated_string = generate_hash_string($row);
    $generated_hash = generate_unique_hash($generated_string);
    
    echo "\nGenerated hash string: '{$generated_string}'\n";
    echo "Generated unique hash: '{$generated_hash}'\n";
    echo "Stored hash string: '{$row['hash_string']}'\n";
    echo "Stored unique hash: '{$row['unique_hash']}'\n";
    
    echo "\nHash string match: " . ($generated_string === $row['hash_string'] ? 'YES' : 'NO') . "\n";
    echo "Unique hash match: " . ($generated_hash === $row['unique_hash'] ? 'YES' : 'NO') . "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nTest completed.\n";
?>
