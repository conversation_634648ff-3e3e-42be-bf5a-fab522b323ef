<?php
/**
 * Test the new database methods for the dump functionality
 */

// Include the necessary files
require_once __DIR__ . '/system/startup_sequence.php';

echo "🧪 Testing Database Methods for Dump Functionality\n";
echo "==================================================\n\n";

try {
    // Test getAllTables method
    echo "1. Testing getAllTables()...\n";
    $tables = system\database::getAllTables();
    echo "✅ Found " . count($tables) . " tables\n";
    echo "   Sample tables: " . implode(', ', array_slice($tables, 0, 5)) . "\n\n";
    
    // Test getTablesLike method for autobooks tables
    echo "2. Testing getTablesLike('autobooks_%')...\n";
    $autobooks_tables = system\database::getTablesLike('autobooks_%');
    echo "✅ Found " . count($autobooks_tables) . " autobooks tables\n";
    if (!empty($autobooks_tables)) {
        echo "   Autobooks tables: " . implode(', ', array_slice($autobooks_tables, 0, 3)) . "\n";
    }
    echo "\n";
    
    // Test getTablesLike method for autodesk tables
    echo "3. Testing getTablesLike('autodesk_%')...\n";
    $autodesk_tables = system\database::getTablesLike('autodesk_%');
    echo "✅ Found " . count($autodesk_tables) . " autodesk tables\n";
    if (!empty($autodesk_tables)) {
        echo "   Autodesk tables: " . implode(', ', array_slice($autodesk_tables, 0, 3)) . "\n";
    }
    echo "\n";
    
    // Test getTableStructure method
    if (!empty($autobooks_tables)) {
        $test_table = $autobooks_tables[0];
        echo "4. Testing getTableStructure('{$test_table}')...\n";
        $structure = system\database::getTableStructure($test_table);
        
        if ($structure) {
            echo "✅ Successfully retrieved table structure\n";
            echo "   Table: " . $structure['Table'] . "\n";
            echo "   Create statement length: " . strlen($structure['Create Table']) . " characters\n";
        } else {
            echo "❌ Failed to get table structure\n";
        }
        echo "\n";
        
        // Test getting table data
        echo "5. Testing database::table('{$test_table}')->get() (limited to 3 records)...\n";
        $data = system\database::table($test_table)->limit(3)->get();
        echo "✅ Retrieved " . count($data) . " records\n";
        if (!empty($data)) {
            echo "   Columns: " . implode(', ', array_keys($data[0])) . "\n";
        }
        echo "\n";
    }
    
    echo "🎉 All database method tests passed!\n";
    echo "\n";
    echo "The database dump system should now work correctly.\n";
    echo "You can access it at: /system/database_dump\n";
    
} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
