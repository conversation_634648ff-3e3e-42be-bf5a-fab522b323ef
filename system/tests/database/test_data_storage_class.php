<?php
/**
 * Test script for data_table_storage class modifications
 * Tests the new data_source_id functionality
 */

require_once 'system/classes/data_table_storage.class.php';

use system\data_table_storage;

echo "Testing data_table_storage class with data_source_id support\n";
echo "=========================================================\n\n";

// Test data
$test_table_name = 'test_table_' . time();
$test_data_source_id = 123;
$test_user_id = 1;
$test_configuration = [
    'structure' => [
        [
            'id' => 'col_1',
            'label' => 'Test Column',
            'field' => 'test_field',
            'visible' => true
        ]
    ],
    'hidden' => [],
    'data_source_type' => 'data_source',
    'data_source_id' => $test_data_source_id
];

echo "1. Testing save_configuration with data_source_id only:\n";
$result = data_table_storage::save_configuration(null, $test_configuration, $test_user_id, $test_data_source_id);
echo "Result: " . ($result ? "SUCCESS" : "FAILED") . "\n\n";

echo "2. Testing get_configuration_by_data_source_id:\n";
$config = data_table_storage::get_configuration_by_data_source_id($test_data_source_id, $test_user_id);
if ($config) {
    echo "SUCCESS - Configuration found\n";
    echo "Data source ID: " . $config['data_source_id'] . "\n";
    echo "Table name: " . ($config['table_name'] ?? 'NULL') . "\n";
} else {
    echo "FAILED - Configuration not found\n";
}
echo "\n";

echo "3. Testing save_configuration with both table_name and data_source_id:\n";
$result = data_table_storage::save_configuration($test_table_name, $test_configuration, $test_user_id, $test_data_source_id);
echo "Result: " . ($result ? "SUCCESS" : "FAILED") . "\n\n";

echo "4. Testing get_configuration with table_name:\n";
$config = data_table_storage::get_configuration($test_table_name, $test_user_id);
if ($config) {
    echo "SUCCESS - Configuration found\n";
    echo "Data source ID: " . $config['data_source_id'] . "\n";
    echo "Table name: " . $config['table_name'] . "\n";
} else {
    echo "FAILED - Configuration not found\n";
}
echo "\n";

echo "5. Testing get_configuration with data_source_id:\n";
$config = data_table_storage::get_configuration(null, $test_user_id, $test_data_source_id);
if ($config) {
    echo "SUCCESS - Configuration found\n";
    echo "Data source ID: " . $config['data_source_id'] . "\n";
    echo "Table name: " . $config['table_name'] . "\n";
} else {
    echo "FAILED - Configuration not found\n";
}
echo "\n";

echo "6. Testing list_configurations_by_data_source_id:\n";
$configs = data_table_storage::list_configurations_by_data_source_id($test_data_source_id, $test_user_id);
echo "Found " . count($configs) . " configuration(s)\n";
foreach ($configs as $config) {
    echo "  - Table: " . ($config['table_name'] ?? 'NULL') . ", Data Source ID: " . $config['data_source_id'] . "\n";
}
echo "\n";

echo "7. Testing has_data_source_configurations:\n";
$has_configs = data_table_storage::has_data_source_configurations($test_data_source_id);
echo "Has configurations: " . ($has_configs ? "YES" : "NO") . "\n\n";

echo "8. Testing backward compatibility - old method calls:\n";
// Test that old method signatures still work
$old_config = data_table_storage::get_configuration($test_table_name, $test_user_id);
echo "Old get_configuration call: " . ($old_config ? "SUCCESS" : "FAILED") . "\n";

$old_save = data_table_storage::save_configuration($test_table_name, $test_configuration, $test_user_id);
echo "Old save_configuration call: " . ($old_save ? "SUCCESS" : "FAILED") . "\n\n";

echo "9. Cleanup - Testing delete_configuration_by_data_source_id:\n";
$result = data_table_storage::delete_configuration_by_data_source_id($test_data_source_id, $test_user_id);
echo "Delete result: " . ($result ? "SUCCESS" : "FAILED") . "\n";

// Verify deletion
$config = data_table_storage::get_configuration_by_data_source_id($test_data_source_id, $test_user_id);
echo "Verification (should be null): " . ($config ? "FAILED - Still exists" : "SUCCESS - Deleted") . "\n\n";

echo "Testing completed!\n";
?>
