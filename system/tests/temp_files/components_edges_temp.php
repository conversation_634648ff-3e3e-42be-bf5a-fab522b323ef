<?php namespace edgeTemplate\email_template_selector;use edge\Edge;?><?php extract(Edge::pp(['edge_manifest' => array (
    'name' => 'email-template-selector',
    'file_path' => 'system/components/edges/email-template-selector.edge.php',
),'name' => 'template',
    'label' => 'Email Template',
    'selected' => '',
    'campaign_id' => null,
    'show_actions' => true,
    'show_data_source' => true,
    'data_source_name' => 'data_source_id',
    'selected_data_source' => '',
    'onchange' => null
],$edge_data));;?>

    <?php
// Get templates from the template folder and subdirectories
$template_folder = FS_APP_ROOT . '/resources/email_templates/';
$templates = [];

if (is_dir($template_folder)) {
    // Get files directly in the template folder
    $files = glob($template_folder . '*.{html,htm,emlt.php}', GLOB_BRACE);
    foreach ($files as $file) {
        $filename = basename($file);
        $name = pathinfo($filename, PATHINFO_FILENAME);
        // Remove .emlt from .emlt.php files
        $name = str_replace('.emlt', '', $name);
        $templates[$filename] = ucwords(str_replace(['_', '-'], ' ', $name));
    }

    // Also get files from subdirectories
    $subdirs = glob($template_folder . '*', GLOB_ONLYDIR);
    foreach ($subdirs as $subdir) {
        $subdir_name = basename($subdir);
        $subfiles = glob($subdir . '/*.{html,htm,emlt.php}', GLOB_BRACE);
        foreach ($subfiles as $file) {
            $filename = $subdir_name . '/' . basename($file);
            $name = pathinfo(basename($file), PATHINFO_FILENAME);
            // Remove .emlt from .emlt.php files
            $name = str_replace('.emlt', '', $name);
            $display_name = ucwords(str_replace(['_', '-'], ' ', $subdir_name)) . ' - ' . ucwords(str_replace(['_', '-'], ' ', $name));
            $templates[$filename] = $display_name;
        }
    }
}

// Also get campaign-specific templates if campaign_id is provided
if ($campaign_id) {
    // This would get templates from the database for this specific campaign
    // For now, we'll just use file-based templates
}
?>

    <div class="space-y-2">
        <div class="flex space-x-2">
            <div class="flex-1">
                <?= Edge::render('forms-select', ["name" => "" . $name . "", "label" => "" . $label . "", 'options' => array_merge(['' => 'Select a template...'], $templates), 'selected' => $selected, "hx-trigger" => "change", 'hx-get' => APP_ROOT . '/api/email_campaigns/template_preview', "hx-target" => "#template_preview", "hx-include" => "this"]) ?>


            </div>


            <?php if($show_data_source): ?>
                <div class="flex-1">
                    <?= Edge::render('data-source-selector', ['name' => $data_source_name, "label" => "Data Source", 'selected' => $selected_data_source, 'show_preview' => false, 'show_create_new' => true]) ?>
                </div>
            <?php endif ?>
        </div>


        <?php if($show_actions): ?>
        <div class="mt-3 flex items-center space-x-3">
            <button type="button"
                    class="text-sm text-indigo-600 hover:text-indigo-500"
                    onclick="previewTemplate()">
                Preview Template
            </button>
            <?php if($show_data_source): ?>
                <span class="text-gray-300">|</span>
                <button type="button"
                        class="text-sm text-indigo-600 hover:text-indigo-500"
                        onclick="testWithDataSource()">
                    Test with Data Source
                </button>
            <?php endif ?>
        </div>
        <?php endif ?>

        <?php if($show_actions): ?>
            <div class="flex space-x-1">

                <button type="button"
                        hx-get="<?= APP_ROOT ?>/api/email_campaigns/edit_template_check"
                        hx-include="#<?= $name ?>"
                        hx-target="#modal_body"
                        hx-vals='{"campaign_id": "<?= $campaign_id ?>"}'
                        @click="showModal = true;"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        title="Edit Selected Template">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </button>


                <button type="button"
                        hx-get="<?= APP_ROOT ?>/api/email_campaigns/template_editor"
                        hx-vals='{"mode": "new", "template": "", "campaign_id": "<?= $campaign_id ?>"}'
                        hx-target="#modal_body"
                        @click="showModal = true;>"
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        title="Create New Template">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </button>
            </div>
        <?php endif ?>
    </div>

    <?php if($show_actions): ?>

        <div id="template_preview" class="hidden mt-2 p-3 bg-gray-50 border rounded-md">
            <div class="text-sm text-gray-600">
                <strong>Preview:</strong>
                <div id="template_preview_content" class="mt-1 max-h-32 overflow-y-auto text-xs"></div>
            </div>
        </div>
    <?php endif ?>
    </div>

    <script>
        function previewTemplate() {
            const templateSelect = document.getElementById('<?= $name ?>');
            const template = templateSelect.value;

            if (!template) {
                alert('Please select a template first');
                return;
            }

            // Open template preview in new window
            window.open(`<?= APP_ROOT ?>/api/email_campaigns/template_preview?template=${template}`, '_blank');
        }

        function testWithDataSource() {
            const templateSelect = document.getElementById('<?= $name ?>');
            const dataSourceSelect = document.getElementById('<?= $data_source_name ?>');

            const template = templateSelect.value;
            const dataSource = dataSourceSelect ? dataSourceSelect.value : '';

            if (!template) {
                alert('Please select a template first');
                return;
            }

            if (!dataSource) {
                alert('Please select a data source to test with');
                return;
            }

            // Open test window with both template and data source
            const url = `<?= APP_ROOT ?>/email_campaigns/test?template=${template}&data_source=${dataSource}`;
            window.open(url, '_blank');
        }
    </script>