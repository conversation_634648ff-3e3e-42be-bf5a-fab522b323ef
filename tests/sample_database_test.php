<?php
/**
 * Sample Database Test
 * This test demonstrates how to create tests that work with the system's database
 */

try {
    echo "Starting database test...\n";
    
    // Test 1: Check if database connection exists
    global $db;
    if (!$db) {
        throw new Exception('Database connection not available');
    }
    echo "✓ Database connection available\n";
    
    // Test 2: Test a simple query
    $query = "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '" . DB_DATABASE . "'";
    $result = tep_db_query($query);
    
    if ($result) {
        $row = tep_db_fetch_array($result);
        echo "✓ Database has {$row['table_count']} tables\n";
    } else {
        throw new Exception('Failed to query database');
    }
    
    // Test 3: Check if specific tables exist (adjust table names as needed)
    $tables_to_check = ['users', 'navigation', 'data_sources'];
    foreach ($tables_to_check as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $result = tep_db_query($query);
        
        if (tep_db_num_rows($result) > 0) {
            echo "✓ Table '$table' exists\n";
        } else {
            echo "⚠ Table '$table' not found\n";
        }
    }
    
    echo "Database test completed successfully!\n";
    return true;
    
} catch (Exception $e) {
    echo "✗ Database test failed: " . $e->getMessage() . "\n";
    return false;
}
?>
