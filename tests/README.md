# Development Testing Suite

This testing suite provides a comprehensive environment for running tests during development without the issues of missing startup sequences, database connections, or CSS loading.

## Features

- **Proper System Integration**: All tests run with full system initialization including database connections, autoloading, and constants
- **Web-based Interface**: Access through the system admin panel at `/system/testing_suite`
- **Multiple Test Types**: Quick tests, custom code tests, and file-based tests
- **Real-time Results**: HTMX-powered interface with immediate feedback
- **Test History**: Automatic logging of test results with timestamps and execution times
- **Safe Execution**: Tests run in controlled environment with proper error handling
- **Organized Structure**: Tests are organized into logical categories for better maintainability

## Access

Navigate to: `{APP_ROOT}/system/testing_suite`

Or access through the System Administration panel → Development Tools → Testing Suite

## Test Organization

All standalone test files have been migrated and organized into the following structure:

### `/tests/` - Main test directory
- `sample_*.php` - Example test files showing best practices
- `README.md` - This documentation file

### `/tests/database/` - Database-related tests
- `test_database_dump.php` - Database dump functionality tests
- `test_database_methods.php` - Database method testing
- `test_database_wherenull.php` - Database null value handling tests
- `test_data_storage_class.php` - Data storage class functionality tests
- `test_hash_generation.php` - Hash generation algorithm tests

### `/tests/data_sources/` - Data source and column management tests
- `test_csv_data_sources.php` - CSV data source handling tests
- `test_column_*.php` - Column analysis and management tests
- `test_import_*.php` - Data import functionality tests
- `test_field_definitions.php` - Field definition system tests
- `test_*_matching.php` - Field matching algorithm tests
- `test_normalization.php` - Data normalization tests
- `debug_sketchup_config.php` - Sketchup configuration debugging
- `simple_column_test.php` - Basic column functionality tests

### `/tests/templates/` - Template system tests
- `test_template_inheritance.php` - Edge template inheritance system tests

### `/tests/temp_files/` - Temporary files for reference
- Copies of temp.php files from various system directories
- These are preserved for debugging and reference purposes

## Test Types

### 1. Quick Tests

Pre-built tests for common system components:

- **Database Connection**: Tests database connectivity, queries, and table existence
- **Edge Templates**: Tests template rendering, compilation, and component system
- **System Classes**: Tests core system classes, constants, and autoloading

### 2. Custom Tests

Write and execute PHP code directly in the browser:

```php
// Example custom test
$result = 'Hello World';
echo "Testing output: $result";
return $result;
```

### 3. Test Files

Execute PHP test files from the filesystem:

- Test files are automatically discovered from `/tests/` and its subdirectories
- Files are recursively scanned and listed in the web interface
- Click on a file name to auto-populate the file path
- Organized structure makes it easy to find specific test types

## Creating Test Files

Test files should be standard PHP files that:

1. Include proper error handling
2. Use `echo` for output messages
3. Return `true` for success, `false` for failure
4. Follow the pattern shown in sample files

### Example Test File Structure

```php
<?php
/**
 * My Custom Test
 * Description of what this test does
 */

try {
    echo "Starting my test...\n";
    
    // Your test logic here
    $result = someFunction();
    
    if ($result) {
        echo "✓ Test passed\n";
        return true;
    } else {
        throw new Exception('Test condition failed');
    }
    
} catch (Exception $e) {
    echo "✗ Test failed: " . $e->getMessage() . "\n";
    return false;
}
?>
```

## Sample Tests

The following sample tests are included:

- `sample_database_test.php` - Database connectivity and table checks
- `sample_edge_template_test.php` - Edge template system functionality
- `sample_system_classes_test.php` - Core system classes and constants

## System Integration

The testing suite automatically provides:

- **Database Connection**: Global `$db` connection available
- **All System Classes**: Autoloaded and ready to use
- **Constants**: All system constants (APP_ROOT, FS_VIEWS, etc.)
- **Session Management**: Active session handling
- **User Authentication**: Admin-level access required
- **Error Handling**: Comprehensive error catching and reporting

## Best Practices

1. **Use Descriptive Names**: Name your tests clearly
2. **Include Documentation**: Add comments explaining what each test does
3. **Handle Errors**: Always wrap tests in try-catch blocks
4. **Clean Up**: Remove temporary files or data created during tests
5. **Return Results**: Always return boolean success/failure
6. **Use Echo for Output**: Provide informative output messages

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure test directories are readable
2. **Database Errors**: Check database connection and credentials
3. **Class Not Found**: Verify autoloading is working correctly
4. **Template Errors**: Check Edge template syntax and file paths

### Debug Information

Each test result includes:
- Execution time in milliseconds
- Timestamp of execution
- Detailed error messages with line numbers
- Full output capture

## Security

- Only admin users can access the testing suite
- Test code execution is sandboxed
- File paths are sanitized to prevent directory traversal
- All test results are logged for audit purposes

## Extending the Suite

To add new quick tests:

1. Edit `system/views/system/testing_suite.api.php`
2. Add a new case in the switch statement
3. Create a corresponding test function
4. Update the UI in `testing_suite.edge.php`

The testing suite is designed to be easily extensible for your specific testing needs.
